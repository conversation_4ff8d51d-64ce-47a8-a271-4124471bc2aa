"""
Audio processing functionality for video cleaning.
"""

import os
import logging
from typing import Optional, Callable
import moviepy.editor as mp

from ..utils.helpers import retry_on_failure, video_clip_manager
from ..utils.cleanup import register_temp_file, unregister_temp_file, safe_remove_file
from ..utils.performance import check_memory, PerformanceProfiler
from .config import get_config

logger = logging.getLogger(__name__)


@retry_on_failure(max_attempts=3, delay=1.0)
def extract_audio(video_path: str, audio_output_path: str, 
                 progress_callback: Optional[Callable[[float, str], None]] = None) -> bool:
    """
    Extracts audio from video file using moviepy with optimized settings
    for maintaining synchronization and timestamp accuracy.
    
    Args:
        video_path: Path to the input video file
        audio_output_path: Path where extracted audio will be saved
        progress_callback: Optional callback for progress updates
        
    Returns:
        True if extraction successful, False otherwise
    """
    config = get_config()
    
    if progress_callback:
        progress_callback(0, f"Starting audio extraction from '{video_path}'...")
    
    logger.info(f"Extracting audio from '{video_path}'...")

    # Check memory before starting
    memory_info = check_memory()
    logger.debug(f"Memory before audio extraction: {memory_info}")

    profiler = PerformanceProfiler()
    profiler.start("audio_extraction")

    try:
        # Check if temp file exists and remove if necessary
        if os.path.exists(audio_output_path):
            logger.warning(f"Temporary audio file '{audio_output_path}' already exists. Removing it.")
            safe_remove_file(audio_output_path)

        # Register temp file for cleanup
        register_temp_file(audio_output_path)

        if progress_callback:
            progress_callback(10, "Loading video file...")

        with video_clip_manager(video_path) as video_clip:
            if video_clip.audio is None:
                logger.error("The video file does not contain an audio track.")
                return False

            # Get original audio properties for logging
            audio_duration = video_clip.audio.duration
            audio_fps = video_clip.audio.fps if hasattr(video_clip.audio, 'fps') else config.audio_sample_rate

            logger.info(f"Original audio: duration={audio_duration:.3f}s, sample_rate={audio_fps}Hz")

            if progress_callback:
                progress_callback(30, "Extracting audio with high quality settings...")

            # Use high quality codec with specific parameters for sync preservation
            video_clip.audio.write_audiofile(
                audio_output_path,
                codec=config.audio_codec,  # Uncompressed 16-bit PCM for maximum quality
                fps=audio_fps,             # Preserve original sample rate
                logger=None,               # Suppress moviepy progress bar
                # FFmpeg parameters for sync preservation during extraction
                ffmpeg_params=[
                    "-avoid_negative_ts", "make_zero",  # Avoid negative timestamps
                    "-fflags", "+genpts"                # Generate presentation timestamps
                ]
            )

            if progress_callback:
                progress_callback(80, "Verifying extracted audio...")

            # Verify extracted audio duration
            extracted_clip = mp.AudioFileClip(audio_output_path)
            extracted_duration = extracted_clip.duration
            extracted_clip.close()

            duration_diff = abs(extracted_duration - audio_duration)
            if duration_diff > 0.01:  # Allow 10ms tolerance
                logger.warning(f"Audio duration mismatch after extraction: "
                             f"original={audio_duration:.3f}s, extracted={extracted_duration:.3f}s "
                             f"(diff={duration_diff:.3f}s)")
            else:
                logger.info(f"Audio extracted successfully: duration={extracted_duration:.3f}s (verified)")

        if progress_callback:
            progress_callback(100, "Audio extraction completed successfully")

        # Log performance stats
        duration = profiler.end("audio_extraction")
        logger.info(f"Audio extracted successfully to '{audio_output_path}' in {duration:.2f}s")

        # Check memory after extraction
        memory_info = check_memory(force_gc=True)
        logger.debug(f"Memory after audio extraction: {memory_info}")

        return True

    except Exception as e:
        logger.exception(f"Error extracting audio: {e}")
        # Clean up partial file if it exists
        safe_remove_file(audio_output_path)
        unregister_temp_file(audio_output_path)
        
        if progress_callback:
            progress_callback(0, f"Audio extraction failed: {e}")
        
        return False


def validate_audio_file(audio_path: str) -> tuple[bool, str]:
    """
    Validate an audio file.
    
    Args:
        audio_path: Path to the audio file
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not os.path.exists(audio_path):
        return False, f"Audio file does not exist: {audio_path}"
    
    try:
        with mp.AudioFileClip(audio_path) as clip:
            if clip.duration <= 0:
                return False, "Audio file has zero duration"
            
            # Check for reasonable duration (not too long)
            max_duration = 24 * 3600  # 24 hours
            if clip.duration > max_duration:
                return False, f"Audio file too long ({clip.duration:.1f}s > {max_duration}s)"
                
        return True, "Valid audio file"
        
    except Exception as e:
        return False, f"Error reading audio file: {e}"
