"""
Video processing functionality for creating muted videos.
"""

import os
import logging
import subprocess
import tempfile
from typing import List, Tuple, Optional, Callable
import numpy as np
import moviepy.editor as mp

from .config import get_config
from ..utils.helpers import retry_on_failure, video_clip_manager

logger = logging.getLogger(__name__)


def validate_video_file(video_path: str) -> tuple[bool, str]:
    """
    Validate a video file.
    
    Args:
        video_path: Path to the video file
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not os.path.exists(video_path):
        return False, f"Video file does not exist: {video_path}"
    
    if not os.path.isfile(video_path):
        return False, f"Path is not a file: {video_path}"
    
    # Check file size
    try:
        size_mb = os.path.getsize(video_path) / (1024 * 1024)
        config = get_config()
        max_size_mb = config.max_video_size_gb * 1024
        
        if size_mb > max_size_mb:
            return False, f"Video file too large ({size_mb:.1f} MB > {max_size_mb:.1f} MB)"
    except OSError as e:
        return False, f"Error checking file size: {e}"
    
    # Try to open with moviepy to validate format
    try:
        with video_clip_manager(video_path) as clip:
            if clip.duration <= 0:
                return False, "Video has zero duration"
            
            if clip.audio is None:
                return False, "Video has no audio track"
                
        return True, "Valid video file"
        
    except Exception as e:
        return False, f"Error reading video file: {e}"


@retry_on_failure(max_attempts=3, delay=2.0)
def create_muted_video(video_path: str, output_path: str, mute_segments: List[Tuple[float, float]],
                      progress_callback: Optional[Callable[[float, str], None]] = None) -> bool:
    """
    Creates the final video with specified audio segments muted using a frame-level approach
    that preserves perfect audio-video synchronization by modifying audio in-place.
    
    Args:
        video_path: Path to the input video file
        output_path: Path where the muted video will be saved
        mute_segments: List of (start, end) time tuples to mute
        progress_callback: Optional callback for progress updates
        
    Returns:
        True if successful, False otherwise
    """
    config = get_config()
    
    if progress_callback:
        progress_callback(0, f"Loading original video '{video_path}' for final processing...")
    
    logger.info(f"Loading original video '{video_path}' for final processing...")
    
    video_clip = None
    original_audio = None
    final_clip = None

    try:
        video_clip = mp.VideoFileClip(video_path)
        original_audio = video_clip.audio
        
        if original_audio is None:
            logger.error("Video has no audio track. Cannot create muted video.")
            return False

        # Get audio properties for precise timing
        audio_duration = original_audio.duration
        audio_fps = original_audio.fps if hasattr(original_audio, 'fps') else config.audio_sample_rate
        video_duration = video_clip.duration
        video_fps = video_clip.fps

        logger.info(f"Original video: duration={video_duration:.3f}s, fps={video_fps}")
        logger.info(f"Original audio: duration={audio_duration:.3f}s, sample_rate={audio_fps}Hz")
        logger.info(f"Applying {len(mute_segments)} mute segments to the audio...")

        if progress_callback:
            progress_callback(10, f"Processing {len(mute_segments)} mute segments...")

        if not mute_segments:
            logger.info("No mute segments to apply. Using original audio.")
            muted_audio = original_audio
        else:
            # Use a more precise frame-level muting approach
            muted_audio = _create_muted_audio(original_audio, mute_segments, progress_callback)
            
            if muted_audio is None:
                logger.error("Failed to create muted audio")
                return False

        # Create the new video with original video stream and processed audio
        target_duration = min(video_duration, audio_duration)
        logger.info(f"Target duration for perfect sync: {target_duration:.3f}s")

        if progress_callback:
            progress_callback(50, "Preparing video and audio streams...")

        # Trim both video and audio to exact same duration if needed
        if abs(video_duration - target_duration) > 0.001:
            logger.info(f"Trimming video to exact duration: {target_duration:.3f}s")
            video_clip = video_clip.set_duration(target_duration)

        if abs(muted_audio.duration - target_duration) > 0.001:
            logger.info(f"Trimming audio to exact duration: {target_duration:.3f}s")
            muted_audio = muted_audio.set_duration(target_duration)

        final_clip = video_clip.set_audio(muted_audio)

        # Final verification
        final_duration = final_clip.duration
        if abs(final_duration - target_duration) > 0.001:
            logger.warning(f"Final clip duration mismatch: target={target_duration:.3f}s, actual={final_duration:.3f}s")
            final_clip = final_clip.set_duration(target_duration)
            logger.info(f"Forced final duration to: {target_duration:.3f}s")
        else:
            logger.info(f"Perfect duration match achieved: {target_duration:.3f}s")

        if progress_callback:
            progress_callback(60, f"Writing muted video to '{output_path}'...")

        logger.info(f"Writing muted video to '{output_path}'...")
        logger.info("Using optimized encoding settings for perfect sync preservation...")

        # Try advanced FFmpeg method first, fallback to standard if needed
        success = _write_video_advanced_method(video_clip, muted_audio, output_path, target_duration, progress_callback)
        
        if not success:
            logger.info("Advanced method failed, trying fallback method...")
            success = _write_video_fallback_method(final_clip, output_path, target_duration, progress_callback)

        if progress_callback:
            if success:
                progress_callback(100, "Video processing completed successfully!")
            else:
                progress_callback(0, "Video processing failed")

        return success

    except Exception as e:
        logger.exception(f"Error creating muted video: {e}")
        if progress_callback:
            progress_callback(0, f"Error creating muted video: {e}")
        return False
        
    finally:
        # Ensure clips are closed to release file handles
        logger.debug("Closing video/audio clips...")
        if original_audio:
            try:
                original_audio.close()
            except:
                pass
        if final_clip:
            try:
                final_clip.close()
            except:
                pass
        if video_clip:
            try:
                video_clip.close()
            except:
                pass
        logger.debug("Clips closed.")


def _create_muted_audio(original_audio, mute_segments: List[Tuple[float, float]],
                       progress_callback: Optional[Callable[[float, str], None]] = None):
    """Create muted audio by applying mute segments to the original audio."""

    def mute_audio_frames(get_frame, t):
        """
        Mute specific time ranges in the audio while preserving exact timing.
        This function processes audio frame-by-frame for perfect sync.
        """
        # Get the original audio frame(s)
        frame = get_frame(t)

        # Handle both scalar and array time inputs
        if isinstance(t, np.ndarray):
            # Array of time values - create a copy to modify
            muted_frame = np.copy(frame)

            # Check each mute segment
            for mute_start, mute_end in mute_segments:
                # Create boolean mask for times within mute range
                mute_mask = (t >= mute_start) & (t < mute_end)

                # Apply muting where mask is True
                if np.any(mute_mask):
                    if muted_frame.ndim == 1:  # Mono audio
                        muted_frame[mute_mask] = 0.0
                    elif muted_frame.ndim == 2:  # Stereo audio
                        muted_frame[mute_mask, :] = 0.0
                    else:
                        logger.warning(f"Unexpected audio frame dimension: {muted_frame.ndim}")

            return muted_frame
        else:
            # Scalar time value
            for mute_start, mute_end in mute_segments:
                if mute_start <= t < mute_end:
                    # Return silence for this time
                    if frame.ndim == 1:  # Mono
                        return np.zeros_like(frame)
                    elif frame.ndim == 2:  # Stereo
                        return np.zeros_like(frame)
                    else:
                        logger.warning(f"Unexpected audio frame dimension: {frame.ndim}")
                        return np.zeros_like(frame)

            # Return original frame if not in mute range
            return frame

    try:
        # Apply the muting function while preserving exact duration and timing
        logger.info("Applying frame-level audio muting for perfect sync preservation...")
        muted_audio = original_audio.fl(mute_audio_frames, keep_duration=True)

        # Verify the duration is preserved exactly
        if abs(muted_audio.duration - original_audio.duration) > 0.001:
            logger.warning(f"Audio duration changed during muting: {original_audio.duration:.3f}s -> {muted_audio.duration:.3f}s")
            # Force exact duration
            muted_audio = muted_audio.set_duration(original_audio.duration)
            logger.info(f"Restored exact audio duration: {original_audio.duration:.3f}s")
        else:
            logger.info(f"Audio duration preserved exactly: {original_audio.duration:.3f}s")

        return muted_audio

    except Exception as e:
        logger.exception(f"Error creating muted audio: {e}")
        return None


def _write_video_advanced_method(video_clip, muted_audio, output_path: str, target_duration: float,
                                progress_callback: Optional[Callable[[float, str], None]] = None) -> bool:
    """Write video using advanced FFmpeg method for best quality and sync."""
    config = get_config()

    try:
        # Create temporary files for precise FFmpeg processing
        output_base, output_ext = os.path.splitext(output_path)
        temp_video_path = f"{output_base}_temp_video{output_ext}"
        temp_audio_path = f"{output_base}_temp_audio.wav"

        if progress_callback:
            progress_callback(65, "Step 1: Writing video stream with exact timing...")

        # Step 1: Write video without audio (preserves exact video timing)
        logger.info("Step 1: Writing video stream with exact timing...")
        video_clip.write_videofile(
            temp_video_path,
            codec=config.video_codec,
            audio=False,  # No audio in this step
            preset=config.video_preset,
            ffmpeg_params=[
                "-crf", config.video_crf,
                "-vsync", "cfr",
                "-avoid_negative_ts", "make_zero",
                "-fflags", "+genpts",
                "-t", f"{target_duration:.6f}"
            ],
            logger=None  # Suppress output
        )

        if progress_callback:
            progress_callback(75, "Step 2: Writing audio stream with exact timing...")

        # Step 2: Write audio with exact timing
        logger.info("Step 2: Writing audio stream with exact timing...")
        audio_fps = muted_audio.fps if hasattr(muted_audio, 'fps') else config.audio_sample_rate
        muted_audio.write_audiofile(
            temp_audio_path,
            codec=config.audio_codec,  # Uncompressed for precision
            fps=audio_fps,
            logger=None,  # Suppress output
            ffmpeg_params=[
                "-avoid_negative_ts", "make_zero",
                "-fflags", "+genpts",
                "-t", f"{target_duration:.6f}"
            ]
        )

        if progress_callback:
            progress_callback(85, "Step 3: Combining video and audio with FFmpeg...")

        # Step 3: Use FFmpeg directly to combine with perfect sync
        logger.info("Step 3: Combining video and audio with FFmpeg for perfect sync...")

        ffmpeg_cmd = [
            'ffmpeg', '-y',  # Overwrite output
            '-i', temp_video_path,  # Video input
            '-i', temp_audio_path,  # Audio input
            '-c:v', 'copy',  # Copy video stream (no re-encoding)
            '-c:a', config.audio_output_codec,   # Encode audio to AAC
            '-map', '0:v:0',  # Map first video stream
            '-map', '1:a:0',  # Map first audio stream
            '-vsync', 'cfr',  # Constant frame rate
            '-async', '1',    # Audio sync correction
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts',
            '-shortest',      # Use shortest stream duration
            '-t', f"{target_duration:.6f}",  # Force exact duration
            output_path
        ]

        logger.debug(f"FFmpeg command: {' '.join(ffmpeg_cmd)}")

        # Check temp file sizes for debugging
        if os.path.exists(temp_video_path) and os.path.exists(temp_audio_path):
            logger.info(f"Temporary video file size: {os.path.getsize(temp_video_path)} bytes")
            logger.info(f"Temporary audio file size: {os.path.getsize(temp_audio_path)} bytes")

        # Run FFmpeg with detailed error handling
        try:
            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=300)  # 5 minute timeout
        except subprocess.TimeoutExpired:
            raise RuntimeError("FFmpeg process timed out after 5 minutes")
        except Exception as e:
            raise RuntimeError(f"Failed to run FFmpeg process: {e}")

        # Log FFmpeg output for debugging
        if result.stdout:
            logger.debug(f"FFmpeg stdout: {result.stdout}")
        if result.stderr:
            logger.debug(f"FFmpeg stderr: {result.stderr}")

        if result.returncode != 0:
            raise RuntimeError(f"FFmpeg failed with return code {result.returncode}: {result.stderr}")

        # Verify output file was created and has reasonable size
        if not os.path.exists(output_path):
            raise RuntimeError("FFmpeg completed but output file was not created")

        output_size = os.path.getsize(output_path)
        if output_size == 0:
            raise RuntimeError("Output file was created but is empty")

        logger.info(f"Advanced FFmpeg method completed successfully. Output size: {output_size} bytes")

        # Clean up temporary files
        for temp_file in [temp_video_path, temp_audio_path]:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    logger.debug(f"Cleaned up temporary file: {temp_file}")
                except Exception as e:
                    logger.warning(f"Could not remove temporary file {temp_file}: {e}")

        return True

    except Exception as e:
        logger.error(f"Advanced FFmpeg method failed: {e}")

        # Clean up temporary files on failure
        output_base, output_ext = os.path.splitext(output_path)
        temp_files = [f"{output_base}_temp_video{output_ext}", f"{output_base}_temp_audio.wav"]
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass

        return False


def _write_video_fallback_method(final_clip, output_path: str, target_duration: float,
                                progress_callback: Optional[Callable[[float, str], None]] = None) -> bool:
    """Write video using standard MoviePy method as fallback."""
    config = get_config()

    try:
        if progress_callback:
            progress_callback(90, "Using fallback method with MoviePy standard encoding...")

        logger.info("Attempting fallback method using MoviePy's standard encoding...")

        # Clean up any partial files from failed attempt
        if os.path.exists(output_path):
            os.remove(output_path)

        # Use standard MoviePy encoding as fallback
        final_clip = final_clip.set_duration(target_duration)

        final_clip.write_videofile(
            output_path,
            codec=config.video_codec,
            audio_codec=config.audio_output_codec,
            preset=config.video_preset,
            ffmpeg_params=[
                "-crf", config.video_crf,
                "-vsync", "cfr",
                "-avoid_negative_ts", "make_zero",
                "-fflags", "+genpts"
            ],
            logger='bar'
        )

        # Verify output
        if not os.path.exists(output_path):
            raise RuntimeError("Fallback method completed but output file was not created")

        output_size = os.path.getsize(output_path)
        if output_size == 0:
            raise RuntimeError("Fallback method created empty output file")

        logger.info(f"Fallback method completed successfully. Output size: {output_size} bytes")
        return True

    except Exception as fallback_error:
        logger.exception(f"Fallback method also failed: {fallback_error}")

        # Clean up incomplete file
        if os.path.exists(output_path):
            logger.warning(f"Attempting to remove incomplete output file: {output_path}")
            try:
                os.remove(output_path)
            except Exception as remove_error:
                logger.warning(f"Could not remove incomplete file: {remove_error}")

        return False
