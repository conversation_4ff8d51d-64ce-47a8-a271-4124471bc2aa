"""
Audio transcription and foul word detection functionality.
"""

import logging
import functools
from typing import List, Dict, Any, Optional, Callable
import stable_whisper
import srt

from .config import get_config
from .data_classes import ProcessingResults, ProcessingStats, WordDetection
from ..utils.helpers import retry_on_failure
from ..utils.performance import check_memory, PerformanceProfiler, clear_moviepy_cache

logger = logging.getLogger(__name__)


@functools.lru_cache(maxsize=2)
def get_whisper_model(model_size: str):
    """Cache loaded Whisper models to avoid reloading."""
    logger.info(f"Loading Whisper model '{model_size}' (cached)...")

    # Check memory before loading model
    memory_info = check_memory()
    logger.debug(f"Memory before model loading: {memory_info}")

    model = stable_whisper.load_model(model_size)

    # Check memory after loading
    memory_info = check_memory(force_gc=True)
    logger.debug(f"Memory after model loading: {memory_info}")

    return model


@retry_on_failure(max_attempts=3, delay=2.0)
def transcribe_audio(audio_path: str, model_size: str, 
                    progress_callback: Optional[Callable[[float, str], None]] = None) -> Optional[List[Dict[str, Any]]]:
    """
    Transcribes audio using Whisper and stable-ts for word timestamps.
    
    Args:
        audio_path: Path to the audio file to transcribe
        model_size: Whisper model size (tiny, base, small, medium, large)
        progress_callback: Optional callback for progress updates
        
    Returns:
        List of word timestamp dictionaries or None if failed
    """
    if progress_callback:
        progress_callback(0, f"Loading Whisper model '{model_size}'...")
    
    logger.info(f"Loading Whisper model '{model_size}'...")
    word_timestamps_data = []

    profiler = PerformanceProfiler()
    profiler.start("transcription")

    try:
        # Load the Whisper model via stable_whisper (cached)
        model = get_whisper_model(model_size)
        
        if progress_callback:
            progress_callback(20, "Starting transcription (this may take a while)...")
        
        logger.info(f"Whisper model '{model_size}' loaded. Starting transcription (this may take a while)...")

        # Transcribe with word-level timestamps using stable-ts refinement
        result = model.transcribe(audio_path, word_timestamps=True, fp16=False)

        if progress_callback:
            progress_callback(80, "Processing transcription results...")

        # Extract word timestamps from the result object
        logger.debug(f"Accessing transcription results from object of type: {type(result)}")
        
        if hasattr(result, 'segments'):
            for segment in result.segments:
                # Access words within the segment object
                words_in_segment = []
                if hasattr(segment, 'words'):
                    words_in_segment = segment.words
                elif isinstance(segment, dict) and 'words' in segment:
                    words_in_segment = segment['words']
                else:
                    logger.warning(f"Segment object (type: {type(segment)}) lacks 'words' attribute or key.")
                    continue

                # Process each word in the segment
                for word_info in words_in_segment:
                    try:
                        # Extract word and timing information
                        if hasattr(word_info, 'word') and hasattr(word_info, 'start') and hasattr(word_info, 'end'):
                            word = word_info.word.strip()
                            start = float(word_info.start)
                            end = float(word_info.end)
                        elif isinstance(word_info, dict):
                            word = word_info.get('word', '').strip()
                            start = float(word_info.get('start', 0))
                            end = float(word_info.get('end', 0))
                        else:
                            logger.warning(f"Unexpected word info format: {word_info}")
                            continue

                        # Validate data
                        if word and start >= 0 and end >= 0:
                            word_timestamps_data.append({'word': word, 'start': start, 'end': end})
                        else:
                            logger.warning(f"Skipping word info due to missing/invalid data: {word_info}")

                    except (AttributeError, KeyError, TypeError, ValueError) as e:
                        logger.warning(f"Error processing word info ({type(e).__name__}: {e}): {word_info}")

        else:
            logger.error("Transcription result object does not have a 'segments' attribute. Cannot extract word timestamps.")
            return None

        if progress_callback:
            progress_callback(100, f"Transcription complete. Found {len(word_timestamps_data)} words.")

        # Log performance stats
        duration = profiler.end("transcription")
        logger.info(f"Transcription complete. Found {len(word_timestamps_data)} words in {duration:.2f}s")

        if not word_timestamps_data:
            logger.warning("Whisper did not detect any words in the audio.")

        # Clear any cached data and check memory
        clear_moviepy_cache()
        memory_info = check_memory(force_gc=True)
        logger.debug(f"Memory after transcription: {memory_info}")

        return word_timestamps_data

    except Exception as e:
        logger.exception(f"Error during transcription: {e}")
        if progress_callback:
            progress_callback(0, f"Transcription failed: {e}")
        return None


def load_foul_words(filepath: str) -> Optional[List[str]]:
    """
    Loads foul words from a file, converts to lowercase.

    Args:
        filepath: Path to the foul words file

    Returns:
        List of foul words in lowercase or None if failed
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            words = [line.strip().lower() for line in f if line.strip()]

        logger.info(f"Loaded {len(words)} foul words from '{filepath}'")
        return words

    except FileNotFoundError:
        logger.error(f"Foul words file not found: '{filepath}'")
        return None
    except Exception as e:
        logger.exception(f"Error loading foul words from '{filepath}': {e}")
        return None


def find_mute_segments_verified(
    word_timestamps: List[Dict[str, Any]],
    foul_words: List[str],
    subtitles: Optional[List[srt.Subtitle]]
) -> ProcessingResults:
    """
    Identifies time segments to mute based on foul words found by Whisper.
    If subtitles are provided, verifies against subtitle content for accuracy.
    If no subtitles are provided, relies solely on Whisper AI detection.
    Returns detailed report data.

    Args:
        word_timestamps: List of word timestamp dictionaries from Whisper
        foul_words: List of foul words to detect (lowercase)
        subtitles: Optional list of SRT subtitle objects for verification

    Returns:
        ProcessingResults containing detection results and mute segments
    """
    config = get_config()

    results = ProcessingResults(
        stats=ProcessingStats(),
        success=True
    )

    if not subtitles:
        # AI-only mode: mute all detected foul words
        logger.info("No subtitles provided. Using AI-only detection mode.")
        for word_data in word_timestamps:
            word_original = word_data['word']
            start = word_data['start']
            end = word_data['end']

            # Clean the word for comparison
            cleaned_word = word_original.lower().strip('.,!?";:()[]{}')

            if cleaned_word in foul_words:
                results.stats.whisper_potential += 1
                results.stats.ai_only_muted += 1

                # Create word detection object
                detection = WordDetection(
                    word=word_original,
                    start=start,
                    end=end,
                    subtitle_text="N/A (AI-only mode)",
                    verified=True,
                    reason="AI-only detection (no subtitle verification)"
                )

                results.whisper_detections.append(detection)
                results.verification_details.append(detection)

                # Create mute segment with padding
                mute_start = max(0, start - config.mute_padding_start)
                mute_end = end + config.mute_padding_end
                results.mute_segments.append((mute_start, mute_end))

                logger.info(f"AI-detected foul word: '{word_original}' ({cleaned_word}) at {start:.2f}s. "
                          f"Marking for mute: {mute_start:.2f}s - {mute_end:.2f}s")

        # Merge overlapping segments
        if results.mute_segments:
            results.mute_segments = _merge_overlapping_segments(results.mute_segments)

        return results

    # Subtitle verification mode
    logger.info("Subtitles provided. Using subtitle verification mode.")
    for word_data in word_timestamps:
        word_original = word_data['word']
        start = word_data['start']
        end = word_data['end']

        # Clean the word for comparison
        cleaned_word = word_original.lower().strip('.,!?";:()[]{}')

        if cleaned_word in foul_words:
            results.stats.whisper_potential += 1

            # Create initial detection
            detection = WordDetection(
                word=word_original,
                start=start,
                end=end
            )
            results.whisper_detections.append(detection)

            # Find overlapping subtitle
            overlapping_subtitle = _find_overlapping_subtitle(start, end, subtitles)

            # Create verification entry
            verification_entry = WordDetection(
                word=word_original,
                start=start,
                end=end,
                subtitle_text=overlapping_subtitle.content if overlapping_subtitle else "No overlapping subtitle",
                verified=False,
                reason=""
            )

            if overlapping_subtitle:
                subtitle_text = overlapping_subtitle.content
                subtitle_text_lower = subtitle_text.lower()

                if cleaned_word in subtitle_text_lower:
                    results.stats.verified_for_mute += 1
                    mute_start = max(0, start - config.mute_padding_start)
                    mute_end = end + config.mute_padding_end
                    results.mute_segments.append((mute_start, mute_end))
                    verification_entry.verified = True
                    verification_entry.reason = "Verified in subtitle"
                    logger.info(f"Verified foul word: '{word_original}' ({cleaned_word}) at {start:.2f}s. "
                              f"Subtitle: \"{subtitle_text}\". Marking for mute: {mute_start:.2f}s - {mute_end:.2f}s")
                else:
                    results.stats.skipped_text_mismatch += 1
                    verification_entry.reason = "Word not found in overlapping subtitle text"
                    logger.info(f"Whisper found '{word_original}' ({start:.2f}s), but not in overlapping subtitle: "
                              f"\"{subtitle_text}\". Skipping mute.")
            else:
                results.stats.skipped_no_subtitle += 1
                verification_entry.reason = "No overlapping subtitle found"
                logger.info(f"Whisper found '{word_original}' ({start:.2f}s), but no overlapping subtitle found. Skipping mute.")

            results.verification_details.append(verification_entry)

    if not results.mute_segments:
        logger.info("No foul words confirmed by subtitles found for muting.")
    else:
        results.mute_segments = _merge_overlapping_segments(results.mute_segments)

    return results


def _find_overlapping_subtitle(start: float, end: float, subtitles: List[srt.Subtitle]) -> Optional[srt.Subtitle]:
    """Find subtitle that overlaps with the given time range."""
    for subtitle in subtitles:
        subtitle_start = subtitle.start.total_seconds()
        subtitle_end = subtitle.end.total_seconds()

        # Check for overlap
        if not (end <= subtitle_start or start >= subtitle_end):
            return subtitle
    return None


def _merge_overlapping_segments(segments: List[tuple]) -> List[tuple]:
    """Merge overlapping time segments."""
    if not segments:
        return []

    segments.sort()
    merged_segments = []
    current_start, current_end = segments[0]

    for next_start, next_end in segments[1:]:
        if next_start <= current_end:
            current_end = max(current_end, next_end)
        else:
            merged_segments.append((current_start, current_end))
            current_start, current_end = next_start, next_end

    merged_segments.append((current_start, current_end))

    if len(merged_segments) < len(segments):
        logger.info(f"Merged overlapping mute segments down to {len(merged_segments)} distinct intervals.")

    return merged_segments
