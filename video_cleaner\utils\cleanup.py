"""
Cleanup utilities for proper resource management and graceful shutdown.
"""

import os
import sys
import atexit
import signal
import logging
import tempfile
import shutil
from typing import List, Set, Callable
from pathlib import Path

logger = logging.getLogger(__name__)


class CleanupManager:
    """Manages cleanup operations for temporary files and resources."""
    
    def __init__(self):
        self._temp_files: Set[str] = set()
        self._temp_dirs: Set[str] = set()
        self._cleanup_callbacks: List[Callable] = []
        self._registered = False
    
    def register_temp_file(self, filepath: str) -> None:
        """Register a temporary file for cleanup."""
        self._temp_files.add(str(Path(filepath).resolve()))
        self._ensure_registered()
    
    def register_temp_dir(self, dirpath: str) -> None:
        """Register a temporary directory for cleanup."""
        self._temp_dirs.add(str(Path(dirpath).resolve()))
        self._ensure_registered()
    
    def register_callback(self, callback: Callable) -> None:
        """Register a cleanup callback function."""
        self._cleanup_callbacks.append(callback)
        self._ensure_registered()
    
    def unregister_temp_file(self, filepath: str) -> None:
        """Unregister a temporary file (e.g., after manual cleanup)."""
        self._temp_files.discard(str(Path(filepath).resolve()))
    
    def unregister_temp_dir(self, dirpath: str) -> None:
        """Unregister a temporary directory (e.g., after manual cleanup)."""
        self._temp_dirs.discard(str(Path(dirpath).resolve()))
    
    def cleanup_now(self) -> None:
        """Perform cleanup immediately."""
        logger.debug("Starting cleanup operations...")
        
        # Run custom cleanup callbacks first
        for callback in self._cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                logger.warning(f"Cleanup callback failed: {e}")
        
        # Clean up temporary files
        for filepath in list(self._temp_files):
            try:
                if os.path.exists(filepath):
                    os.remove(filepath)
                    logger.debug(f"Cleaned up temporary file: {filepath}")
                self._temp_files.discard(filepath)
            except Exception as e:
                logger.warning(f"Failed to remove temporary file {filepath}: {e}")
        
        # Clean up temporary directories
        for dirpath in list(self._temp_dirs):
            try:
                if os.path.exists(dirpath):
                    shutil.rmtree(dirpath, ignore_errors=True)
                    logger.debug(f"Cleaned up temporary directory: {dirpath}")
                self._temp_dirs.discard(dirpath)
            except Exception as e:
                logger.warning(f"Failed to remove temporary directory {dirpath}: {e}")
        
        logger.debug("Cleanup operations completed")
    
    def _ensure_registered(self) -> None:
        """Ensure cleanup handlers are registered."""
        if not self._registered:
            atexit.register(self.cleanup_now)
            
            # Register signal handlers for graceful shutdown
            if hasattr(signal, 'SIGINT'):
                signal.signal(signal.SIGINT, self._signal_handler)
            if hasattr(signal, 'SIGTERM'):
                signal.signal(signal.SIGTERM, self._signal_handler)
            
            self._registered = True
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, performing cleanup...")
        self.cleanup_now()
        sys.exit(0)


# Global cleanup manager instance
_cleanup_manager = CleanupManager()


def register_temp_file(filepath: str) -> None:
    """Register a temporary file for automatic cleanup."""
    _cleanup_manager.register_temp_file(filepath)


def register_temp_dir(dirpath: str) -> None:
    """Register a temporary directory for automatic cleanup."""
    _cleanup_manager.register_temp_dir(dirpath)


def register_cleanup_callback(callback: Callable) -> None:
    """Register a cleanup callback function."""
    _cleanup_manager.register_callback(callback)


def unregister_temp_file(filepath: str) -> None:
    """Unregister a temporary file."""
    _cleanup_manager.unregister_temp_file(filepath)


def unregister_temp_dir(dirpath: str) -> None:
    """Unregister a temporary directory."""
    _cleanup_manager.unregister_temp_dir(dirpath)


def cleanup_now() -> None:
    """Perform cleanup immediately."""
    _cleanup_manager.cleanup_now()


def create_temp_file(suffix: str = "", prefix: str = "video_cleaner_", 
                    dir: str = None, auto_register: bool = True) -> str:
    """
    Create a temporary file and optionally register it for cleanup.
    
    Args:
        suffix: File suffix
        prefix: File prefix
        dir: Directory to create file in
        auto_register: Whether to automatically register for cleanup
        
    Returns:
        Path to the temporary file
    """
    fd, filepath = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
    os.close(fd)  # Close the file descriptor, we just need the path
    
    if auto_register:
        register_temp_file(filepath)
    
    return filepath


def create_temp_dir(suffix: str = "", prefix: str = "video_cleaner_", 
                   dir: str = None, auto_register: bool = True) -> str:
    """
    Create a temporary directory and optionally register it for cleanup.
    
    Args:
        suffix: Directory suffix
        prefix: Directory prefix
        dir: Parent directory
        auto_register: Whether to automatically register for cleanup
        
    Returns:
        Path to the temporary directory
    """
    dirpath = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=dir)
    
    if auto_register:
        register_temp_dir(dirpath)
    
    return dirpath


class TempFileContext:
    """Context manager for temporary files with automatic cleanup."""
    
    def __init__(self, suffix: str = "", prefix: str = "video_cleaner_", dir: str = None):
        self.suffix = suffix
        self.prefix = prefix
        self.dir = dir
        self.filepath = None
    
    def __enter__(self) -> str:
        self.filepath = create_temp_file(
            suffix=self.suffix, 
            prefix=self.prefix, 
            dir=self.dir, 
            auto_register=False
        )
        return self.filepath
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.filepath and os.path.exists(self.filepath):
            try:
                os.remove(self.filepath)
            except Exception as e:
                logger.warning(f"Failed to cleanup temporary file {self.filepath}: {e}")


class TempDirContext:
    """Context manager for temporary directories with automatic cleanup."""
    
    def __init__(self, suffix: str = "", prefix: str = "video_cleaner_", dir: str = None):
        self.suffix = suffix
        self.prefix = prefix
        self.dir = dir
        self.dirpath = None
    
    def __enter__(self) -> str:
        self.dirpath = create_temp_dir(
            suffix=self.suffix, 
            prefix=self.prefix, 
            dir=self.dir, 
            auto_register=False
        )
        return self.dirpath
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.dirpath and os.path.exists(self.dirpath):
            try:
                shutil.rmtree(self.dirpath, ignore_errors=True)
            except Exception as e:
                logger.warning(f"Failed to cleanup temporary directory {self.dirpath}: {e}")


def safe_remove_file(filepath: str) -> bool:
    """
    Safely remove a file with error handling.
    
    Args:
        filepath: Path to the file to remove
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if os.path.exists(filepath):
            os.remove(filepath)
            logger.debug(f"Successfully removed file: {filepath}")
            return True
        return True  # File doesn't exist, consider it success
    except Exception as e:
        logger.warning(f"Failed to remove file {filepath}: {e}")
        return False


def safe_remove_dir(dirpath: str) -> bool:
    """
    Safely remove a directory with error handling.
    
    Args:
        dirpath: Path to the directory to remove
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if os.path.exists(dirpath):
            shutil.rmtree(dirpath, ignore_errors=True)
            logger.debug(f"Successfully removed directory: {dirpath}")
            return True
        return True  # Directory doesn't exist, consider it success
    except Exception as e:
        logger.warning(f"Failed to remove directory {dirpath}: {e}")
        return False
