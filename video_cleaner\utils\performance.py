"""
Performance optimization utilities for video processing.
"""

import gc
import psutil
import logging
import time
from typing import List, Tuple, Optional, Generator, Callable
from dataclasses import dataclass
import moviepy.editor as mp

from ..core.config import get_config

logger = logging.getLogger(__name__)


@dataclass
class MemoryInfo:
    """Information about memory usage."""
    total_mb: float
    available_mb: float
    used_mb: float
    percent_used: float
    
    @classmethod
    def current(cls) -> 'MemoryInfo':
        """Get current memory information."""
        memory = psutil.virtual_memory()
        return cls(
            total_mb=memory.total / (1024 * 1024),
            available_mb=memory.available / (1024 * 1024),
            used_mb=memory.used / (1024 * 1024),
            percent_used=memory.percent
        )
    
    def __str__(self) -> str:
        return f"Memory: {self.used_mb:.1f}MB/{self.total_mb:.1f}MB ({self.percent_used:.1f}%)"


class MemoryMonitor:
    """Monitor memory usage and trigger cleanup when needed."""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.last_warning_time = 0
        self.warning_interval = 30  # seconds
    
    def check_memory(self, force_gc: bool = False) -> MemoryInfo:
        """Check current memory usage and trigger cleanup if needed."""
        memory_info = MemoryInfo.current()
        current_time = time.time()
        
        if memory_info.percent_used >= self.critical_threshold:
            logger.warning(f"Critical memory usage: {memory_info}")
            self._force_cleanup()
        elif memory_info.percent_used >= self.warning_threshold:
            # Only log warning once per interval to avoid spam
            if current_time - self.last_warning_time > self.warning_interval:
                logger.warning(f"High memory usage: {memory_info}")
                self.last_warning_time = current_time
            if force_gc:
                self._gentle_cleanup()
        
        return memory_info
    
    def _gentle_cleanup(self):
        """Perform gentle garbage collection."""
        logger.debug("Performing gentle memory cleanup...")
        gc.collect()
    
    def _force_cleanup(self):
        """Perform aggressive memory cleanup."""
        logger.warning("Performing aggressive memory cleanup...")
        gc.collect()
        gc.collect()  # Run twice for better cleanup
        gc.collect()


# Global memory monitor
_memory_monitor = MemoryMonitor()


def get_memory_info() -> MemoryInfo:
    """Get current memory information."""
    return MemoryInfo.current()


def check_memory(force_gc: bool = False) -> MemoryInfo:
    """Check memory and trigger cleanup if needed."""
    return _memory_monitor.check_memory(force_gc)


def estimate_video_memory_usage(video_path: str) -> float:
    """
    Estimate memory usage for processing a video file.
    
    Args:
        video_path: Path to the video file
        
    Returns:
        Estimated memory usage in MB
    """
    try:
        with mp.VideoFileClip(video_path) as clip:
            # Rough estimation based on video properties
            duration = clip.duration
            fps = clip.fps
            width, height = clip.size
            
            # Estimate memory for video frames (assuming RGB, 3 bytes per pixel)
            frame_size_mb = (width * height * 3) / (1024 * 1024)
            
            # Estimate for audio (assuming 16-bit stereo at 44.1kHz)
            audio_size_mb = (duration * 44100 * 2 * 2) / (1024 * 1024)
            
            # Add overhead for processing (rough estimate)
            overhead_factor = 2.5
            
            total_estimate = (frame_size_mb + audio_size_mb) * overhead_factor
            
            logger.debug(f"Memory estimate for {video_path}: {total_estimate:.1f}MB")
            return total_estimate
            
    except Exception as e:
        logger.warning(f"Could not estimate memory usage for {video_path}: {e}")
        return 1000.0  # Conservative estimate


def should_use_chunked_processing(video_path: str) -> bool:
    """
    Determine if chunked processing should be used based on video size and available memory.
    
    Args:
        video_path: Path to the video file
        
    Returns:
        True if chunked processing is recommended
    """
    config = get_config()
    memory_info = get_memory_info()
    estimated_usage = estimate_video_memory_usage(video_path)
    
    # Use chunked processing if:
    # 1. Video is longer than chunk duration threshold
    # 2. Estimated memory usage exceeds 50% of available memory
    # 3. Available memory is less than 4GB
    
    try:
        with mp.VideoFileClip(video_path) as clip:
            duration = clip.duration
            
            if duration > config.chunk_duration_seconds:
                logger.info(f"Using chunked processing: video duration ({duration:.1f}s) exceeds threshold ({config.chunk_duration_seconds}s)")
                return True
            
            if estimated_usage > memory_info.available_mb * 0.5:
                logger.info(f"Using chunked processing: estimated usage ({estimated_usage:.1f}MB) exceeds 50% of available memory ({memory_info.available_mb:.1f}MB)")
                return True
            
            if memory_info.available_mb < 4096:  # Less than 4GB available
                logger.info(f"Using chunked processing: low available memory ({memory_info.available_mb:.1f}MB)")
                return True
                
    except Exception as e:
        logger.warning(f"Could not determine chunked processing need for {video_path}: {e}")
        return True  # Conservative approach
    
    return False


def generate_video_chunks(video_path: str, chunk_duration: Optional[float] = None) -> Generator[Tuple[float, float], None, None]:
    """
    Generate time chunks for processing a video.
    
    Args:
        video_path: Path to the video file
        chunk_duration: Duration of each chunk in seconds
        
    Yields:
        Tuples of (start_time, end_time) for each chunk
    """
    if chunk_duration is None:
        chunk_duration = get_config().chunk_duration_seconds
    
    try:
        with mp.VideoFileClip(video_path) as clip:
            total_duration = clip.duration
            
            current_start = 0.0
            chunk_count = 0
            
            while current_start < total_duration:
                chunk_end = min(current_start + chunk_duration, total_duration)
                chunk_count += 1
                
                logger.debug(f"Generated chunk {chunk_count}: {current_start:.2f}s - {chunk_end:.2f}s")
                yield (current_start, chunk_end)
                
                current_start = chunk_end
                
            logger.info(f"Generated {chunk_count} chunks for video processing")
            
    except Exception as e:
        logger.error(f"Error generating video chunks for {video_path}: {e}")
        # Fallback: yield the entire video as one chunk
        yield (0.0, float('inf'))


class PerformanceProfiler:
    """Simple performance profiler for timing operations."""
    
    def __init__(self):
        self.timings = {}
        self.start_times = {}
    
    def start(self, operation: str):
        """Start timing an operation."""
        self.start_times[operation] = time.time()
    
    def end(self, operation: str) -> float:
        """End timing an operation and return duration."""
        if operation not in self.start_times:
            logger.warning(f"No start time recorded for operation: {operation}")
            return 0.0
        
        duration = time.time() - self.start_times[operation]
        
        if operation not in self.timings:
            self.timings[operation] = []
        self.timings[operation].append(duration)
        
        del self.start_times[operation]
        return duration
    
    def get_stats(self, operation: str) -> dict:
        """Get statistics for an operation."""
        if operation not in self.timings:
            return {}
        
        timings = self.timings[operation]
        return {
            "count": len(timings),
            "total": sum(timings),
            "average": sum(timings) / len(timings),
            "min": min(timings),
            "max": max(timings)
        }
    
    def log_summary(self):
        """Log a summary of all recorded timings."""
        logger.info("Performance Summary:")
        for operation, timings in self.timings.items():
            stats = self.get_stats(operation)
            logger.info(f"  {operation}: {stats['count']} calls, avg {stats['average']:.2f}s, total {stats['total']:.2f}s")


class ProgressiveProcessor:
    """Base class for progressive processing with memory management."""
    
    def __init__(self, progress_callback: Optional[Callable] = None):
        self.progress_callback = progress_callback
        self.memory_monitor = MemoryMonitor()
        self.profiler = PerformanceProfiler()
        self.cancelled = False
    
    def cancel(self):
        """Cancel the processing operation."""
        self.cancelled = True
    
    def update_progress(self, percentage: float, message: str):
        """Update progress and check memory."""
        if self.progress_callback:
            self.progress_callback(percentage, message)
        
        # Check memory every 10% progress
        if int(percentage) % 10 == 0:
            self.memory_monitor.check_memory(force_gc=True)
    
    def process_with_monitoring(self, operation_name: str, operation_func: Callable, *args, **kwargs):
        """Execute an operation with performance monitoring."""
        self.profiler.start(operation_name)
        try:
            result = operation_func(*args, **kwargs)
            return result
        finally:
            duration = self.profiler.end(operation_name)
            logger.debug(f"Operation '{operation_name}' completed in {duration:.2f}s")


def optimize_moviepy_settings():
    """Apply optimizations to MoviePy settings."""
    try:
        # Set MoviePy to use less memory for preview generation
        import moviepy.config as mp_config
        mp_config.IMAGEIO_FFMPEG_EXE = "ffmpeg"  # Ensure ffmpeg is used
        
        logger.debug("Applied MoviePy optimizations")
    except Exception as e:
        logger.warning(f"Could not apply MoviePy optimizations: {e}")


def clear_moviepy_cache():
    """Clear MoviePy's internal caches."""
    try:
        # Force garbage collection to clear any cached clips
        gc.collect()
        logger.debug("Cleared MoviePy caches")
    except Exception as e:
        logger.warning(f"Error clearing MoviePy caches: {e}")
