import whisper
import stable_whisper
import moviepy.editor as mp
import argparse
import os
import sys
import time
import numpy as np
import srt
from datetime import timed<PERSON><PERSON>
from typing import List, Tuple, Optional, Dict, Any
import logging # Import logging library
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext, font
import threading
from queue import Queue, Empty

# --- Configuration ---
DEFAULT_MODEL_SIZE = "medium"
TEMP_AUDIO_FILENAME = "temp_audio_for_processing.wav"
OUTPUT_FILENAME_SUFFIX = "_muted_verified"
REPORT_FILENAME_SUFFIX = "_report.log"
LOG_FILENAME_SUFFIX = "_processing.log"
MUTE_PADDING_START = 0.05
MUTE_PADDING_END = 0.05

# --- Logging Setup ---
logger = logging.getLogger(__name__)

def setup_logging(log_file_path: str, level=logging.INFO):
    """Configures logging to file and console."""
    logger.setLevel(level)
    # Prevent adding multiple handlers if called again
    if logger.hasHandlers():
        logger.handlers.clear()

    # File Handler
    log_dir = os.path.dirname(log_file_path)
    if log_dir and not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except OSError as e:
            print(f"Warning: Could not create log directory '{log_dir}'. Logging to current directory. Error: {e}")
            log_file_path = os.path.basename(log_file_path)

    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # Console Handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = logging.Formatter('%(levelname)s: %(message)s')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

# --- Helper Functions ---

def load_foul_words(filepath: str) -> Optional[List[str]]:
    """Loads foul words from a file, converts to lowercase."""
    logger.info(f"Loading foul words from '{filepath}'...")
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            words = [line.strip().lower() for line in f if line.strip()]
        if not words:
            logger.warning(f"Foul words file '{filepath}' is empty or contains no valid words.")
        else:
             logger.info(f"Loaded {len(words)} words to mute.")
        return words
    except FileNotFoundError:
        logger.error(f"Foul words file not found at '{filepath}'")
        return None
    except Exception as e:
        logger.exception(f"Error reading foul words file '{filepath}': {e}")
        return None

def parse_subtitles(srt_path: str) -> Optional[List[srt.Subtitle]]:
    """Parses an SRT subtitle file."""
    logger.info(f"Parsing subtitle file '{srt_path}'...")
    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            subtitle_content = f.read()
            # Basic check for empty file before parsing
            if not subtitle_content.strip():
                 logger.warning(f"Subtitle file '{srt_path}' is empty.")
                 return [] # Return empty list, not None, to distinguish from read errors

            subtitle_generator = srt.parse(subtitle_content)
            subtitles = list(subtitle_generator) # Consume generator

            if not subtitles:
                # This might happen if the format is invalid despite content being present
                logger.warning(f"Could not parse any valid subtitle entries from '{srt_path}'. File might be malformed.")
                return []
            logger.info(f"Parsed {len(subtitles)} subtitle entries.")
            return subtitles
    except FileNotFoundError:
        logger.error(f"Subtitle file not found at '{srt_path}'")
        return None
    except Exception as e:
        logger.exception(f"Error parsing subtitle file '{srt_path}': {e}")
        return None

def extract_audio(video_path: str, audio_output_path: str) -> bool:
    """
    Extracts audio from video file using moviepy with optimized settings
    for maintaining synchronization and timestamp accuracy.
    """
    logger.info(f"Extracting audio from '{video_path}'...")
    try:
        # Check if temp file exists and remove if necessary
        if os.path.exists(audio_output_path):
            logger.warning(f"Temporary audio file '{audio_output_path}' already exists. Removing it.")
            os.remove(audio_output_path)

        video_clip = mp.VideoFileClip(video_path)
        if video_clip.audio is None:
            logger.error("The video file does not contain an audio track.")
            video_clip.close()
            return False

        # Get original audio properties for logging
        audio_duration = video_clip.audio.duration
        audio_fps = video_clip.audio.fps if hasattr(video_clip.audio, 'fps') else 44100

        logger.info(f"Original audio: duration={audio_duration:.3f}s, sample_rate={audio_fps}Hz")

        # Use high quality codec with specific parameters for sync preservation
        video_clip.audio.write_audiofile(
            audio_output_path,
            codec='pcm_s16le',  # Uncompressed 16-bit PCM for maximum quality
            fps=audio_fps,      # Preserve original sample rate
            logger=None,        # Suppress moviepy progress bar
            # FFmpeg parameters for sync preservation during extraction
            ffmpeg_params=[
                "-avoid_negative_ts", "make_zero",  # Avoid negative timestamps
                "-fflags", "+genpts"                # Generate presentation timestamps
            ]
        )

        video_clip.close()  # Close the video file handle

        # Verify extracted audio duration
        extracted_clip = mp.AudioFileClip(audio_output_path)
        extracted_duration = extracted_clip.duration
        extracted_clip.close()

        duration_diff = abs(extracted_duration - audio_duration)
        if duration_diff > 0.01:  # Allow 10ms tolerance
            logger.warning(f"Audio duration mismatch after extraction: original={audio_duration:.3f}s, extracted={extracted_duration:.3f}s (diff={duration_diff:.3f}s)")
        else:
            logger.info(f"Audio extracted successfully: duration={extracted_duration:.3f}s (verified)")

        logger.info(f"Audio extracted successfully to '{audio_output_path}'")
        return True

    except Exception as e:
        logger.exception(f"Error extracting audio: {e}")
        # Clean up partial file if it exists
        if os.path.exists(audio_output_path):
            try:
                os.remove(audio_output_path)
            except Exception as rm_e:
                logger.warning(f"Could not remove partial temp audio file '{audio_output_path}': {rm_e}")
        return False

def transcribe_audio(audio_path: str, model_size: str) -> Optional[List[Dict[str, Any]]]:
    """Transcribes audio using Whisper and stable-ts for word timestamps."""
    logger.info(f"Loading Whisper model '{model_size}'...")
    word_timestamps_data = []
    try:
        # Load the Whisper model via stable_whisper
        # Note: stable_whisper enhances the model object loaded from whisper
        model = stable_whisper.load_model(model_size)
        logger.info(f"Whisper model '{model_size}' loaded. Starting transcription (this may take a while)...")

        # Transcribe with word-level timestamps using stable-ts refinement
        # Use fp16=False for CPU-only inference
        # Call the transcribe method on the loaded model object
        result = model.transcribe(audio_path, word_timestamps=True, fp16=False)

        # Extract word timestamps from the result object
        logger.debug(f"Accessing transcription results from object of type: {type(result)}")
        if hasattr(result, 'segments'): # Check if the result object has a 'segments' attribute
            for segment in result.segments: # Access the segments attribute directly
                # Now access words within the segment object
                words_in_segment = []
                if hasattr(segment, 'words'):
                    words_in_segment = segment.words
                elif isinstance(segment, dict) and 'words' in segment: # Fallback if segment is dict-like
                    words_in_segment = segment['words']
                else:
                    logger.warning(f"Segment object (type: {type(segment)}) lacks 'words' attribute or key.")
                    continue # Skip segment if words cannot be accessed

                for word_info in words_in_segment:
                    # Access word data - might be object attributes or dict keys
                    word, start, end = None, -1.0, -1.0
                    try:
                        if hasattr(word_info, 'word') and hasattr(word_info, 'start') and hasattr(word_info, 'end'):
                            # Try accessing as attributes
                            word = word_info.word.strip()
                            start = float(word_info.start)
                            end = float(word_info.end)
                        elif isinstance(word_info, dict) and all(k in word_info for k in ['word', 'start', 'end']):
                            # Try accessing as dictionary keys
                            word = word_info['word'].strip()
                            start = float(word_info['start'])
                            end = float(word_info['end'])

                        # Validate data
                        if word and start >= 0 and end >= 0:
                            word_timestamps_data.append({'word': word, 'start': start, 'end': end})
                        else:
                            logger.warning(f"Skipping word info due to missing/invalid data: {word_info}")

                    except (AttributeError, KeyError, TypeError, ValueError) as e:
                        logger.warning(f"Error processing word info ({type(e).__name__}: {e}): {word_info}")

        else:
            logger.error("Transcription result object does not have a 'segments' attribute. Cannot extract word timestamps.")
            return None # Indicate failure if segments cannot be accessed

        logger.info(f"Transcription complete. Found {len(word_timestamps_data)} words.")
        if not word_timestamps_data:
             logger.warning("Whisper did not detect any words in the audio.")
        return word_timestamps_data

    except ImportError as e:
         logger.critical(f"Import error during transcription setup: {e}. Is PyTorch or Whisper installed correctly?")
         return None
    except Exception as e:
        logger.exception(f"Error during transcription: {e}")
        return None

def find_overlapping_subtitle(word_start: float, word_end: float, subtitles: List[srt.Subtitle]) -> Optional[srt.Subtitle]:
    """Finds the first subtitle entry that overlaps with the given word time."""
    for sub in subtitles:
        sub_start = sub.start.total_seconds()
        sub_end = sub.end.total_seconds()
        # Check for overlap: max(start1, start2) < min(end1, end2)
        # Ensure word_start < word_end and sub_start < sub_end for valid comparison
        if word_start < word_end and sub_start < sub_end and max(word_start, sub_start) < min(word_end, sub_end):
            return sub
    return None

def find_mute_segments_verified(
    word_timestamps: List[Dict[str, Any]],
    foul_words: List[str],
    subtitles: Optional[List[srt.Subtitle]]
) -> Dict[str, Any]:
    """
    Identifies time segments to mute based on foul words found by Whisper.
    If subtitles are provided, verifies against subtitle content for accuracy.
    If no subtitles are provided, relies solely on Whisper AI detection.
    Returns detailed report data.
    """
    results = {
        "whisper_detections": [], # List of all potential foul words found by Whisper
        "verification_details": [], # List of details for each verification attempt
        "mute_segments": [], # Final list of segments to mute
        "stats": {"whisper_potential": 0, "verified_for_mute": 0, "skipped_no_subtitle": 0, "skipped_text_mismatch": 0, "ai_only_muted": 0}
    }

    # Handle subtitle-free processing (AI-only mode)
    if subtitles is None or len(subtitles) == 0:
        logger.info("No subtitles provided - using AI-only foul word detection mode.")
        logger.info("All Whisper-detected foul words will be muted without subtitle verification.")

        for word_data in word_timestamps:
            word_original = word_data['word']
            word_lower = word_original.strip().lower()
            cleaned_word = ''.join(filter(str.isalnum, word_lower))
            start = word_data['start']
            end = word_data['end']

            if cleaned_word in foul_words:
                results["stats"]["whisper_potential"] += 1
                results["stats"]["ai_only_muted"] += 1
                results["whisper_detections"].append(word_data)

                # Create mute segment with padding
                mute_start = max(0, start - MUTE_PADDING_START)
                mute_end = end + MUTE_PADDING_END
                results["mute_segments"].append((mute_start, mute_end))

                # Add verification details for reporting
                results["verification_details"].append({
                    **word_data,
                    "subtitle_text": "N/A (AI-only mode)",
                    "verified": True,
                    "reason": "AI-only detection (no subtitle verification)"
                })

                logger.info(f"AI-detected foul word: '{word_original}' ({cleaned_word}) at {start:.2f}s. Marking for mute: {mute_start:.2f}s - {mute_end:.2f}s")

        logger.info(f"AI-only mode: Found {results['stats']['ai_only_muted']} foul words to mute based on Whisper detection.")

        # Merge overlapping segments
        if results["mute_segments"]:
            results["mute_segments"].sort()
            merged_segments = []
            current_start, current_end = results["mute_segments"][0]
            for next_start, next_end in results["mute_segments"][1:]:
                if next_start <= current_end:
                    current_end = max(current_end, next_end)
                else:
                    merged_segments.append((current_start, current_end))
                    current_start, current_end = next_start, next_end
            merged_segments.append((current_start, current_end))
            if len(merged_segments) < len(results["mute_segments"]):
                logger.info(f"Merged overlapping mute segments down to {len(merged_segments)} distinct intervals.")
            results["mute_segments"] = merged_segments

        return results

    logger.info("Starting verification of Whisper detections against subtitles...")
    for word_data in word_timestamps:
        word_original = word_data['word']
        word_lower = word_original.strip().lower()
        start = word_data['start']
        end = word_data['end']

        cleaned_word = ''.join(filter(str.isalnum, word_lower))

        if cleaned_word in foul_words:
            results["stats"]["whisper_potential"] += 1
            results["whisper_detections"].append(word_data)

            verification_entry = {**word_data, "subtitle_text": "N/A", "verified": False, "reason": ""}
            overlapping_sub = find_overlapping_subtitle(start, end, subtitles)

            if overlapping_sub:
                subtitle_text = overlapping_sub.content.strip()
                subtitle_text_lower = subtitle_text.lower()
                verification_entry["subtitle_text"] = subtitle_text

                if cleaned_word in subtitle_text_lower:
                    results["stats"]["verified_for_mute"] += 1
                    mute_start = max(0, start - MUTE_PADDING_START)
                    mute_end = end + MUTE_PADDING_END
                    results["mute_segments"].append((mute_start, mute_end))
                    verification_entry["verified"] = True
                    verification_entry["reason"] = "Verified in subtitle"
                    logger.info(f"Verified foul word: '{word_original}' ({cleaned_word}) at {start:.2f}s. Subtitle: \"{subtitle_text}\". Marking for mute: {mute_start:.2f}s - {mute_end:.2f}s")
                else:
                    results["stats"]["skipped_text_mismatch"] += 1
                    verification_entry["reason"] = "Word not found in overlapping subtitle text"
                    logger.info(f"Whisper found '{word_original}' ({start:.2f}s), but not in overlapping subtitle: \"{subtitle_text}\". Skipping mute.")
            else:
                results["stats"]["skipped_no_subtitle"] += 1
                verification_entry["reason"] = "No overlapping subtitle found for this timestamp"
                logger.info(f"Whisper found '{word_original}' ({start:.2f}s), but no overlapping subtitle found. Skipping mute.")

            results["verification_details"].append(verification_entry)

    logger.info("-" * 10)
    logger.info(f"Whisper identified {results['stats']['whisper_potential']} potential foul words.")
    if subtitles is not None:
        logger.info(f"Verified {results['stats']['verified_for_mute']} instances against subtitles for muting.")
        logger.info(f"Skipped {results['stats']['skipped_text_mismatch']} instances due to text mismatch in subtitles.")
        logger.info(f"Skipped {results['stats']['skipped_no_subtitle']} instances due to no overlapping subtitle found.")
    else:
         logger.warning("Subtitle verification was skipped as subtitles could not be loaded.")

    if not results["mute_segments"]:
        logger.info("No foul words confirmed by subtitles found for muting.")
    else:
        results["mute_segments"].sort()
        merged_segments = []
        if results["mute_segments"]:
            current_start, current_end = results["mute_segments"][0]
            for next_start, next_end in results["mute_segments"][1:]:
                if next_start <= current_end:
                    current_end = max(current_end, next_end)
                else:
                    merged_segments.append((current_start, current_end))
                    current_start, current_end = next_start, next_end
            merged_segments.append((current_start, current_end))
            if len(merged_segments) < len(results["mute_segments"]):
                logger.info(f"Merged overlapping mute segments down to {len(merged_segments)} distinct intervals.")
            results["mute_segments"] = merged_segments

    return results


def create_muted_video(video_path: str, output_path: str, mute_segments: List[Tuple[float, float]]) -> bool:
    """
    Creates the final video with specified audio segments muted using a frame-level approach
    that preserves perfect audio-video synchronization by modifying audio in-place.
    """
    logger.info(f"Loading original video '{video_path}' for final processing...")
    video_clip = None
    original_audio = None
    final_clip = None

    try:
        video_clip = mp.VideoFileClip(video_path)
        original_audio = video_clip.audio
        if original_audio is None:
            logger.error("Video has no audio track. Cannot create muted video.")
            return False

        # Get audio properties for precise timing
        audio_duration = original_audio.duration
        audio_fps = original_audio.fps if hasattr(original_audio, 'fps') else 44100
        video_duration = video_clip.duration
        video_fps = video_clip.fps

        logger.info(f"Original video: duration={video_duration:.3f}s, fps={video_fps}")
        logger.info(f"Original audio: duration={audio_duration:.3f}s, sample_rate={audio_fps}Hz")
        logger.info(f"Applying {len(mute_segments)} mute segments to the audio...")

        if not mute_segments:
            logger.info("No mute segments to apply. Using original audio.")
            muted_audio = original_audio
        else:
            # Use a more precise frame-level muting approach
            def mute_audio_frames(get_frame, t):
                """
                Mute specific time ranges in the audio while preserving exact timing.
                This function processes audio frame-by-frame for perfect sync.
                """
                # Get the original audio frame(s)
                frame = get_frame(t)

                # Handle both scalar and array time inputs
                if isinstance(t, np.ndarray):
                    # Array of time values - create a copy to modify
                    muted_frame = np.copy(frame)

                    # Check each mute segment
                    for mute_start, mute_end in mute_segments:
                        # Create boolean mask for times within mute range
                        mute_mask = (t >= mute_start) & (t < mute_end)

                        # Apply muting where mask is True
                        if np.any(mute_mask):
                            if muted_frame.ndim == 1:  # Mono audio
                                muted_frame[mute_mask] = 0.0
                            elif muted_frame.ndim == 2:  # Stereo audio
                                muted_frame[mute_mask, :] = 0.0
                            else:
                                logger.warning(f"Unexpected audio frame dimension: {muted_frame.ndim}")

                    return muted_frame
                else:
                    # Scalar time value
                    for mute_start, mute_end in mute_segments:
                        if mute_start <= t < mute_end:
                            # Return silence for this time point
                            return np.zeros_like(frame)

                    # Return original frame if not in mute range
                    return frame

            # Apply the muting function while preserving exact duration and timing
            logger.info("Applying frame-level audio muting for perfect sync preservation...")
            muted_audio = original_audio.fl(mute_audio_frames, keep_duration=True)

            # Verify the duration is preserved exactly
            if abs(muted_audio.duration - audio_duration) > 0.001:
                logger.warning(f"Audio duration changed during muting: {audio_duration:.3f}s -> {muted_audio.duration:.3f}s")
                # Force exact duration
                muted_audio = muted_audio.set_duration(audio_duration)
                logger.info(f"Restored exact audio duration: {audio_duration:.3f}s")
            else:
                logger.info(f"Audio duration preserved exactly: {audio_duration:.3f}s")

        # Create the new video with original video stream and processed audio
        # Ensure both video and audio have exactly the same duration
        target_duration = min(video_duration, audio_duration)
        logger.info(f"Target duration for perfect sync: {target_duration:.3f}s")

        # Trim both video and audio to exact same duration if needed
        if abs(video_duration - target_duration) > 0.001:
            logger.info(f"Trimming video to exact duration: {target_duration:.3f}s")
            video_clip = video_clip.set_duration(target_duration)

        if abs(muted_audio.duration - target_duration) > 0.001:
            logger.info(f"Trimming audio to exact duration: {target_duration:.3f}s")
            muted_audio = muted_audio.set_duration(target_duration)

        final_clip = video_clip.set_audio(muted_audio)

        # Final verification
        final_duration = final_clip.duration
        if abs(final_duration - target_duration) > 0.001:
            logger.warning(f"Final clip duration mismatch: target={target_duration:.3f}s, actual={final_duration:.3f}s")
            final_clip = final_clip.set_duration(target_duration)
            logger.info(f"Forced final duration to: {target_duration:.3f}s")
        else:
            logger.info(f"Perfect duration match achieved: {target_duration:.3f}s")

        logger.info(f"Writing muted video to '{output_path}'...")
        logger.info("Using optimized encoding settings for perfect sync preservation...")

        # Create temporary files for precise FFmpeg processing
        # Get the base name and extension for proper temp file naming
        output_base, output_ext = os.path.splitext(output_path)
        temp_video_path = f"{output_base}_temp_video{output_ext}"
        temp_audio_path = f"{output_base}_temp_audio.wav"

        try:
            # Step 1: Write video without audio (preserves exact video timing)
            logger.info("Step 1: Writing video stream with exact timing...")
            video_clip.write_videofile(
                temp_video_path,
                codec='libx264',
                audio=False,  # No audio in this step
                preset='medium',
                ffmpeg_params=[
                    "-crf", "18",
                    "-vsync", "cfr",
                    "-avoid_negative_ts", "make_zero",
                    "-fflags", "+genpts",
                    "-t", f"{target_duration:.6f}"
                ],
                logger=None  # Suppress output
            )

            # Step 2: Write audio with exact timing
            logger.info("Step 2: Writing audio stream with exact timing...")
            muted_audio.write_audiofile(
                temp_audio_path,
                codec='pcm_s16le',  # Uncompressed for precision
                fps=audio_fps,
                logger=None,  # Suppress output
                ffmpeg_params=[
                    "-avoid_negative_ts", "make_zero",
                    "-fflags", "+genpts",
                    "-t", f"{target_duration:.6f}"
                ]
            )

            # Step 3: Use FFmpeg directly to combine with perfect sync
            logger.info("Step 3: Combining video and audio with FFmpeg for perfect sync...")
            import subprocess

            ffmpeg_cmd = [
                'ffmpeg', '-y',  # Overwrite output
                '-i', temp_video_path,  # Video input
                '-i', temp_audio_path,  # Audio input
                '-c:v', 'copy',  # Copy video stream (no re-encoding)
                '-c:a', 'aac',   # Encode audio to AAC
                '-map', '0:v:0',  # Map first video stream
                '-map', '1:a:0',  # Map first audio stream
                '-vsync', 'cfr',  # Constant frame rate
                '-async', '1',    # Audio sync correction
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts',
                '-shortest',      # Use shortest stream duration
                '-t', f"{target_duration:.6f}",  # Force exact duration
                output_path
            ]

            logger.info(f"Running FFmpeg command: {' '.join(ffmpeg_cmd)}")

            # Check if temporary files exist before running FFmpeg
            if not os.path.exists(temp_video_path):
                raise RuntimeError(f"Temporary video file not found: {temp_video_path}")
            if not os.path.exists(temp_audio_path):
                raise RuntimeError(f"Temporary audio file not found: {temp_audio_path}")

            logger.info(f"Temporary video file size: {os.path.getsize(temp_video_path)} bytes")
            logger.info(f"Temporary audio file size: {os.path.getsize(temp_audio_path)} bytes")

            # Run FFmpeg with detailed error handling
            try:
                result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=300)  # 5 minute timeout
            except subprocess.TimeoutExpired:
                raise RuntimeError("FFmpeg process timed out after 5 minutes")
            except Exception as e:
                raise RuntimeError(f"Failed to run FFmpeg process: {e}")

            # Log FFmpeg output for debugging
            if result.stdout:
                logger.debug(f"FFmpeg stdout: {result.stdout}")
            if result.stderr:
                logger.debug(f"FFmpeg stderr: {result.stderr}")

            if result.returncode != 0:
                logger.error(f"FFmpeg failed with return code {result.returncode}")
                logger.error(f"FFmpeg stderr: {result.stderr}")
                logger.error(f"FFmpeg stdout: {result.stdout}")
                raise RuntimeError(f"FFmpeg encoding failed (return code {result.returncode}): {result.stderr}")

            # Verify output file was created
            if not os.path.exists(output_path):
                raise RuntimeError(f"Output file was not created: {output_path}")

            output_size = os.path.getsize(output_path)
            logger.info(f"FFmpeg encoding completed successfully. Output file size: {output_size} bytes")

        finally:
            # Clean up temporary files
            for temp_file in [temp_video_path, temp_audio_path]:
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                        logger.debug(f"Removed temporary file: {temp_file}")
                    except Exception as e:
                        logger.warning(f"Could not remove temporary file {temp_file}: {e}")

        logger.info("Muted video saved successfully with perfect lip-sync preservation.")
        return True

    except Exception as e:
        logger.error(f"Advanced FFmpeg method failed: {e}")
        logger.info("Attempting fallback method using MoviePy's standard encoding...")

        # Fallback to standard MoviePy encoding
        try:
            # Clean up any partial files from failed attempt
            if os.path.exists(output_path):
                os.remove(output_path)

            # Use standard MoviePy encoding as fallback
            final_clip = video_clip.set_audio(muted_audio)
            final_clip = final_clip.set_duration(target_duration)

            final_clip.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                preset='medium',
                ffmpeg_params=[
                    "-crf", "18",
                    "-vsync", "cfr",
                    "-avoid_negative_ts", "make_zero",
                    "-fflags", "+genpts"
                ],
                logger='bar'
            )

            logger.info("Fallback method completed successfully.")
            return True

        except Exception as fallback_error:
            logger.exception(f"Fallback method also failed: {fallback_error}")
            if os.path.exists(output_path):
                logger.warning(f"Attempting to remove incomplete output file: {output_path}")
                try:
                    os.remove(output_path)
                except Exception as remove_error:
                    logger.warning(f"Could not remove incomplete file: {remove_error}")
            return False
    finally:
        # Ensure clips are closed to release file handles
        logger.debug("Closing video/audio clips...")
        if original_audio:
            original_audio.close()
        if final_clip:
            final_clip.close()
        if video_clip:
            video_clip.close()
        logger.debug("Clips closed.")

def generate_report(report_path: str, args, results: Dict[str, Any], total_time: float, success: bool):
    """Generates a detailed text report of the process."""
    logger.info(f"Generating report file at '{report_path}'...")
    try:
        report_dir = os.path.dirname(report_path)
        if report_dir and not os.path.exists(report_dir):
             try:
                 os.makedirs(report_dir)
             except OSError as e:
                  logger.warning(f"Could not create report directory '{report_dir}'. Saving report to current directory. Error: {e}")
                  report_path = os.path.basename(report_path)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("--- Mute Words Verification Report ---\n\n")
            f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Video File: {getattr(args, 'video_path', 'N/A')}\n")
            f.write(f"Subtitle File: {getattr(args, 'srt_path', 'N/A')}\n")
            f.write(f"Foul Words File: {getattr(args, 'words_file', 'N/A')}\n")
            f.write(f"Whisper Model: {getattr(args, 'model', 'N/A')}\n")
            # Use the determined output path stored in args
            f.write(f"Output File: {getattr(args, 'output_path', 'N/A')}\n")
            f.write(f"Log File: {getattr(args, 'log_file', 'N/A')}\n")
            f.write("-" * 30 + "\n\n")

            f.write("--- Summary ---\n")
            stats = results.get("stats", {})
            f.write(f"Overall Status: {'Success' if success else 'Failed'}\n")
            f.write(f"Total Processing Time: {total_time:.2f} seconds\n")

            # Determine processing mode
            subtitle_file = getattr(args, 'srt_path', 'N/A')
            if subtitle_file and subtitle_file != 'N/A':
                f.write(f"Processing Mode: Subtitle Verification\n")
                f.write(f"Whisper - Potential Foul Words Found: {stats.get('whisper_potential', 0)}\n")
                f.write(f"Verification - Instances Verified for Mute: {stats.get('verified_for_mute', 0)}\n")
                f.write(f"Verification - Skipped (Text Mismatch): {stats.get('skipped_text_mismatch', 0)}\n")
                f.write(f"Verification - Skipped (No Overlapping Subtitle): {stats.get('skipped_no_subtitle', 0)}\n")
            else:
                f.write(f"Processing Mode: AI-Only Detection\n")
                f.write(f"Whisper - Potential Foul Words Found: {stats.get('whisper_potential', 0)}\n")
                f.write(f"AI-Only - Instances Muted: {stats.get('ai_only_muted', 0)}\n")

            f.write(f"Total Mute Segments Applied (after merging): {len(results.get('mute_segments', []))}\n")
            f.write("-" * 30 + "\n\n")

            f.write("--- Whisper Foul Word Detections ---\n")
            if results.get("whisper_detections"):
                for item in results["whisper_detections"]:
                    f.write(f"- Word: '{item['word']}', Time: {item['start']:.3f}s - {item['end']:.3f}s\n")
            else:
                f.write("No potential foul words detected by Whisper.\n")
            f.write("-" * 30 + "\n\n")

            f.write("--- Verification Details ---\n")
            if results.get("verification_details"):
                for item in results["verification_details"]:
                    f.write(f"- Word: '{item['word']}' ({item['start']:.3f}s - {item['end']:.3f}s)\n")
                    f.write(f"  Subtitle: \"{item['subtitle_text']}\"\n")
                    f.write(f"  Verified: {item['verified']} ({item['reason']})\n")
            else:
                f.write("No verification performed (likely due to missing subtitles or no Whisper detections).\n")
            f.write("-" * 30 + "\n\n")

            f.write("--- Final Muted Segments (Start Time, End Time) ---\n")
            if results.get("mute_segments"):
                for start, end in results["mute_segments"]:
                    f.write(f"- {start:.3f}s - {end:.3f}s\n")
            else:
                f.write("No segments were muted.\n")
            f.write("-" * 30 + "\n")
        logger.info("Report generated successfully.")
    except Exception as e:
        logger.exception(f"Failed to generate report file at '{report_path}': {e}")


# --- GUI Classes ---

class QueueHandler(logging.Handler):
    """Custom logging handler that puts log records into a queue for GUI display."""

    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(self.format(record))


class VideoCleanerGUI:
    """Main GUI application for the Video Cleaner."""

    def __init__(self, root):
        self.root = root
        self.root.title("🎬 Video Cleaner - AI-Powered Foul Word Muter")
        self.root.geometry("1000x750")
        self.root.minsize(900, 650)

        # Configure modern styling
        self.setup_styles()

        # Initialize variables
        self.video_path = tk.StringVar()
        self.srt_path = tk.StringVar()

        # Set default foul words file if it exists
        default_words_file = "foul_words.txt"
        if os.path.exists(default_words_file):
            self.words_file = tk.StringVar(value=default_words_file)
        else:
            self.words_file = tk.StringVar()

        self.model_size = tk.StringVar(value=DEFAULT_MODEL_SIZE)
        self.output_path = tk.StringVar()
        self.log_level = tk.StringVar(value="INFO")

        # Processing state
        self.processing = False
        self.processing_thread = None
        self.log_queue = Queue()

        # Setup GUI
        self.setup_gui()
        self.setup_logging()

        # Start log monitoring
        self.monitor_log_queue()

    def setup_styles(self):
        """Configure modern styling for the application."""
        # Configure the style
        style = ttk.Style()

        # Use a modern theme
        try:
            style.theme_use('clam')  # Modern looking theme
        except:
            style.theme_use('default')

        # Define color scheme
        self.colors = {
            'primary': '#2E86AB',      # Blue
            'secondary': '#A23B72',    # Purple
            'success': '#F18F01',      # Orange
            'background': '#F5F5F5',   # Light gray
            'surface': '#FFFFFF',      # White
            'text': '#2C3E50',         # Dark blue-gray
            'text_light': '#7F8C8D',   # Light gray
            'accent': '#E74C3C',       # Red
            'border': '#BDC3C7'        # Light border
        }

        # Configure root window
        self.root.configure(bg=self.colors['background'])

        # Configure custom styles
        style.configure('Title.TLabel',
                       font=('Segoe UI', 16, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['background'])

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       foreground=self.colors['text'],
                       background=self.colors['surface'])

        style.configure('Info.TLabel',
                       font=('Segoe UI', 9),
                       foreground=self.colors['text_light'],
                       background=self.colors['surface'])

        style.configure('Status.TLabel',
                       font=('Segoe UI', 10, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['surface'])

        style.configure('Modern.TFrame',
                       background=self.colors['surface'],
                       relief='flat',
                       borderwidth=1)

        style.configure('Modern.TLabelFrame',
                       background=self.colors['surface'],
                       relief='solid',
                       borderwidth=1)

        style.configure('Modern.TLabelFrame.Label',
                       font=('Segoe UI', 11, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['surface'])

        style.configure('Primary.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='white',
                       background=self.colors['primary'],
                       borderwidth=0,
                       focuscolor='none')

        style.map('Primary.TButton',
                 background=[('active', '#1F5F7A'),
                           ('pressed', '#1A4F66')])

        style.configure('Success.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='white',
                       background=self.colors['success'],
                       borderwidth=0,
                       focuscolor='none')

        style.map('Success.TButton',
                 background=[('active', '#D17A01'),
                           ('pressed', '#B86801')])

        style.configure('Danger.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='white',
                       background=self.colors['accent'],
                       borderwidth=0,
                       focuscolor='none')

        style.map('Danger.TButton',
                 background=[('active', '#C0392B'),
                           ('pressed', '#A93226')])

        style.configure('Modern.TEntry',
                       fieldbackground='white',
                       borderwidth=1,
                       relief='solid',
                       insertcolor=self.colors['primary'])

        style.configure('Modern.TCombobox',
                       fieldbackground='white',
                       borderwidth=1,
                       relief='solid')

        style.configure('Modern.Horizontal.TProgressbar',
                       background=self.colors['primary'],
                       troughcolor=self.colors['border'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])

    def setup_gui(self):
        """Create and layout the GUI components."""
        # Create header
        self.create_header()

        # Create main notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # Main tab
        main_frame = ttk.Frame(notebook, style='Modern.TFrame')
        notebook.add(main_frame, text="🎯 Main")

        # Log tab
        log_frame = ttk.Frame(notebook, style='Modern.TFrame')
        notebook.add(log_frame, text="📋 Logs")

        self.setup_main_tab(main_frame)
        self.setup_log_tab(log_frame)

    def create_header(self):
        """Create the application header."""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(header_frame,
                              text="🎬 Video Cleaner",
                              font=('Segoe UI', 20, 'bold'),
                              fg='white',
                              bg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, padx=20, pady=20)

        # Subtitle
        subtitle_label = tk.Label(header_frame,
                                 text="AI-Powered Foul Word Detection & Muting",
                                 font=('Segoe UI', 11),
                                 fg='white',
                                 bg=self.colors['primary'])
        subtitle_label.pack(side=tk.LEFT, padx=(0, 20), pady=(25, 15))

    def setup_main_tab(self, parent):
        """Setup the main processing tab."""
        # Create scrollable frame
        canvas = tk.Canvas(parent, bg=self.colors['background'])
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Modern.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # File selection section
        file_frame = ttk.LabelFrame(scrollable_frame, text="📁 File Selection", padding=20)
        file_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Video file
        self.create_file_input(file_frame, "🎬 Video File:", self.video_path,
                              self.browse_video_file, 0,
                              "Select your video file (MP4, AVI, MOV, etc.)")

        # Subtitle file
        self.create_file_input(file_frame, "📝 Subtitle File (.srt) - Optional:", self.srt_path,
                              self.browse_srt_file, 1,
                              "Optional: Select subtitle file for verification (improves accuracy). Leave empty for AI-only detection.")

        # Foul words file
        self.create_file_input(file_frame, "🚫 Foul Words File:", self.words_file,
                              self.browse_words_file, 2,
                              "Text file containing words to mute")

        # Output file
        self.create_file_input(file_frame, "💾 Output File:", self.output_path,
                              self.browse_output_file, 3,
                              "Where to save the processed video")

        # Configuration section
        config_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ Configuration", padding=20)
        config_frame.pack(fill=tk.X, padx=15, pady=10)

        # Model selection
        ttk.Label(config_frame, text="🤖 AI Model:", font=('Segoe UI', 12, 'bold')).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 5))

        model_frame = ttk.Frame(config_frame)
        model_frame.grid(row=1, column=0, sticky=tk.W+tk.E, pady=(0, 15))

        model_combo = ttk.Combobox(model_frame, textvariable=self.model_size,
                                  values=["tiny", "base", "small", "medium", "large"],
                                  state="readonly", width=15)
        model_combo.pack(side=tk.LEFT)

        ttk.Label(model_frame, text="(medium recommended for best balance)",
                 font=('Segoe UI', 9), foreground='gray').pack(side=tk.LEFT, padx=(10, 0))

        # Log level
        ttk.Label(config_frame, text="📊 Log Level:", font=('Segoe UI', 12, 'bold')).grid(
            row=0, column=1, sticky=tk.W, padx=(30, 0), pady=(0, 5))

        log_frame = ttk.Frame(config_frame)
        log_frame.grid(row=1, column=1, sticky=tk.W, padx=(30, 0), pady=(0, 15))

        log_combo = ttk.Combobox(log_frame, textvariable=self.log_level,
                                values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                                state="readonly", width=12)
        log_combo.pack(side=tk.LEFT)

        # Progress section
        progress_frame = ttk.LabelFrame(scrollable_frame, text="📈 Progress", padding=20)
        progress_frame.pack(fill=tk.X, padx=15, pady=10)

        self.status_label = ttk.Label(progress_frame, text="Ready to process",
                                     font=('Segoe UI', 10, 'bold'))
        self.status_label.pack(anchor=tk.W, pady=(0, 10))

        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))

        # Control buttons
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill=tk.X, padx=15, pady=(10, 20))

        self.start_button = ttk.Button(button_frame, text="🚀 Start Processing",
                                      command=self.start_processing, style="Primary.TButton")
        self.start_button.pack(side=tk.LEFT, padx=(0, 10), ipadx=20, ipady=8)

        self.stop_button = ttk.Button(button_frame, text="⏹️ Stop Processing",
                                     command=self.stop_processing, state=tk.DISABLED,
                                     style="Danger.TButton")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10), ipadx=20, ipady=8)

        ttk.Button(button_frame, text="📂 Open Output Folder",
                  command=self.open_output_folder, style="Success.TButton").pack(
                  side=tk.RIGHT, ipadx=15, ipady=8)

    def create_file_input(self, parent, label_text, var, browse_command, row, tooltip_text):
        """Create a modern file input row."""
        # Label
        ttk.Label(parent, text=label_text, font=('Segoe UI', 12, 'bold')).grid(
            row=row*2, column=0, sticky=tk.W, pady=(0, 5))

        # Input frame
        input_frame = ttk.Frame(parent)
        input_frame.grid(row=row*2+1, column=0, sticky=tk.W+tk.E, pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)

        # Entry
        entry = ttk.Entry(input_frame, textvariable=var, width=70)
        entry.grid(row=0, column=0, sticky=tk.W+tk.E, padx=(0, 10))

        # Browse button
        browse_btn = ttk.Button(input_frame, text="📁 Browse", command=browse_command)
        browse_btn.grid(row=0, column=1, ipadx=10)

        # Tooltip
        ttk.Label(parent, text=tooltip_text, font=('Segoe UI', 9), foreground='gray').grid(
            row=row*2+2, column=0, sticky=tk.W, pady=(0, 10))

    def setup_log_tab(self, parent):
        """Setup the log viewing tab."""
        # Header section
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        ttk.Label(header_frame, text="📋 Processing Logs",
                 font=('Segoe UI', 16, 'bold')).pack(side=tk.LEFT)

        # Control buttons
        button_frame = ttk.Frame(header_frame)
        button_frame.pack(side=tk.RIGHT)

        ttk.Button(button_frame, text="🗑️ Clear Logs",
                  command=self.clear_logs).pack(side=tk.RIGHT, ipadx=10)

        # Log display section
        log_frame = ttk.LabelFrame(parent, text="📄 Real-time Logs", padding=15)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # Create scrolled text widget for logs with modern styling
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            wrap=tk.WORD,
            height=25,
            font=('Consolas', 10),
            bg='#1E1E1E',  # Dark background
            fg='#D4D4D4',  # Light text
            insertbackground='#FFFFFF',  # White cursor
            selectbackground='#264F78',  # Blue selection
            relief='flat',
            borderwidth=0
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Configure text tags for different log levels
        self.log_text.tag_configure("INFO", foreground="#4FC3F7")     # Light blue
        self.log_text.tag_configure("WARNING", foreground="#FFB74D")  # Orange
        self.log_text.tag_configure("ERROR", foreground="#F44336")    # Red
        self.log_text.tag_configure("CRITICAL", foreground="#E91E63", background="#FFEBEE")  # Pink
        self.log_text.tag_configure("DEBUG", foreground="#9E9E9E")    # Gray

        # Info section
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        ttk.Label(info_frame,
                 text="💡 Tip: Logs are displayed in real-time during processing. Different colors indicate log levels.",
                 font=('Segoe UI', 9), foreground='gray').pack(anchor=tk.W)

    def setup_logging(self):
        """Setup logging to display in GUI."""
        # Clear any existing handlers
        logger.handlers.clear()

        # Add queue handler for GUI
        queue_handler = QueueHandler(self.log_queue)
        queue_handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
        logger.addHandler(queue_handler)
        logger.setLevel(getattr(logging, self.log_level.get().upper()))

    def monitor_log_queue(self):
        """Monitor the log queue and update GUI with colored logs."""
        try:
            while True:
                record = self.log_queue.get_nowait()

                # Determine log level for coloring
                level = "INFO"  # default
                if "ERROR:" in record:
                    level = "ERROR"
                elif "WARNING:" in record:
                    level = "WARNING"
                elif "CRITICAL:" in record:
                    level = "CRITICAL"
                elif "DEBUG:" in record:
                    level = "DEBUG"

                # Insert with appropriate tag
                start_pos = self.log_text.index(tk.END)
                self.log_text.insert(tk.END, record + '\n')
                end_pos = self.log_text.index(tk.END)

                # Apply color tag
                self.log_text.tag_add(level, start_pos, end_pos)

                # Auto-scroll to bottom
                self.log_text.see(tk.END)
        except Empty:
            pass
        finally:
            # Schedule next check
            self.root.after(100, self.monitor_log_queue)

    def browse_video_file(self):
        """Browse for video file."""
        filename = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.video_path.set(filename)
            self.auto_set_output_path()

    def browse_srt_file(self):
        """Browse for subtitle file."""
        filename = filedialog.askopenfilename(
            title="Select Subtitle File",
            filetypes=[("Subtitle files", "*.srt"), ("All files", "*.*")]
        )
        if filename:
            self.srt_path.set(filename)

    def browse_words_file(self):
        """Browse for foul words file."""
        filename = filedialog.askopenfilename(
            title="Select Foul Words File",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.words_file.set(filename)

    def browse_output_file(self):
        """Browse for output file location."""
        filename = filedialog.asksaveasfilename(
            title="Save Output Video As",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.output_path.set(filename)

    def auto_set_output_path(self):
        """Automatically set output path based on input video."""
        if self.video_path.get() and not self.output_path.get():
            video_file = self.video_path.get()
            base_name = os.path.splitext(video_file)[0]
            ext = os.path.splitext(video_file)[1]
            output_file = f"{base_name}{OUTPUT_FILENAME_SUFFIX}{ext}"
            self.output_path.set(output_file)

    def clear_logs(self):
        """Clear the log display."""
        self.log_text.delete(1.0, tk.END)

    def open_output_folder(self):
        """Open the folder containing the output file."""
        output_file = self.output_path.get()
        if output_file and os.path.exists(output_file):
            folder = os.path.dirname(output_file)
            if sys.platform == "win32":
                os.startfile(folder)
            elif sys.platform == "darwin":
                os.system(f"open '{folder}'")
            else:
                os.system(f"xdg-open '{folder}'")
        else:
            messagebox.showwarning("Warning", "Output file does not exist yet.")

    def validate_inputs(self):
        """Validate all required inputs."""
        # Check video file
        if not self.video_path.get().strip():
            messagebox.showerror("Error", "Please select a video file.")
            return False

        video_path = self.video_path.get().strip()
        if not os.path.exists(video_path):
            messagebox.showerror("Error", f"Video file does not exist:\n{video_path}")
            return False

        if not os.path.isfile(video_path):
            messagebox.showerror("Error", f"Video path is not a file:\n{video_path}")
            return False

        # Check subtitle file (optional)
        srt_path = self.srt_path.get().strip()
        if srt_path and not os.path.exists(srt_path):
            messagebox.showerror("Error", f"Subtitle file does not exist:\n{srt_path}")
            return False

        if srt_path and not os.path.isfile(srt_path):
            messagebox.showerror("Error", f"Subtitle path is not a file:\n{srt_path}")
            return False

        # Check foul words file
        if not self.words_file.get().strip():
            messagebox.showerror("Error", "Please select a foul words file.")
            return False

        words_path = self.words_file.get().strip()
        if not os.path.exists(words_path):
            messagebox.showerror("Error", f"Foul words file does not exist:\n{words_path}\n\nPlease create this file or select an existing one.")
            return False

        if not os.path.isfile(words_path):
            messagebox.showerror("Error", f"Foul words path is not a file:\n{words_path}")
            return False

        # Check output path
        if not self.output_path.get().strip():
            messagebox.showerror("Error", "Please specify an output file path.")
            return False

        output_path = self.output_path.get().strip()
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            messagebox.showerror("Error", f"Output directory does not exist:\n{output_dir}")
            return False

        # Check if output file already exists
        if os.path.exists(output_path):
            result = messagebox.askyesno(
                "File Exists",
                f"Output file already exists:\n{output_path}\n\nOverwrite?"
            )
            if not result:
                return False

        return True

    def start_processing(self):
        """Start the video processing in a separate thread."""
        if not self.validate_inputs():
            return

        if self.processing:
            messagebox.showwarning("Warning", "Processing is already in progress.")
            return

        # Update logging level
        logger.setLevel(getattr(logging, self.log_level.get().upper()))

        # Clear logs
        self.clear_logs()

        # Update UI state
        self.processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        self.status_label.config(text="Processing...")

        # Start processing thread
        self.processing_thread = threading.Thread(target=self.process_video, daemon=True)
        self.processing_thread.start()

    def stop_processing(self):
        """Stop the current processing (note: this is a request, actual stopping depends on the processing stage)."""
        if self.processing:
            self.processing = False
            self.status_label.config(text="Stopping...")
            logger.warning("Stop requested by user. Processing will stop at the next safe point.")

    def processing_finished(self, success, message):
        """Called when processing is complete."""
        self.processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()

        if success:
            self.status_label.config(text="Processing completed successfully!")
            messagebox.showinfo("Success", message)
        else:
            self.status_label.config(text="Processing failed!")
            messagebox.showerror("Error", message)

    def process_video(self):
        """Main video processing method that runs in a separate thread."""
        start_time = time.time()
        success = False
        results_data = {}

        try:
            # Create a simple args object for compatibility with existing functions
            class Args:
                def __init__(self, gui):
                    self.video_path = os.path.abspath(gui.video_path.get())

                    # Handle optional subtitle file
                    if gui.srt_path.get().strip():
                        self.srt_path = os.path.abspath(gui.srt_path.get())
                    else:
                        self.srt_path = None

                    self.words_file = os.path.abspath(gui.words_file.get())
                    self.model = gui.model_size.get()
                    self.output_path = os.path.abspath(gui.output_path.get())

                    # Generate log and report file paths
                    base_filename = os.path.splitext(os.path.basename(self.video_path))[0]
                    input_dir = os.path.dirname(self.video_path)
                    self.log_file = os.path.join(input_dir, f"{base_filename}{LOG_FILENAME_SUFFIX}")
                    self.report_file = os.path.join(input_dir, f"{base_filename}{REPORT_FILENAME_SUFFIX}")

            args = Args(self)

            # Update status
            self.root.after(0, lambda: self.status_label.config(text="Loading foul words..."))

            # 1. Load Foul Words
            logger.info("-" * 20 + " Step 1: Load Foul Words " + "-" * 20)
            foul_words = load_foul_words(args.words_file)
            if foul_words is None:
                raise RuntimeError("Failed to load foul words.")
            if not foul_words:
                logger.warning("Foul words list is empty. No words will be muted.")

            if not self.processing:
                return

            # Update status
            if args.srt_path:
                self.root.after(0, lambda: self.status_label.config(text="Parsing subtitles..."))
            else:
                self.root.after(0, lambda: self.status_label.config(text="Skipping subtitles (AI-only mode)..."))

            # 2. Parse Subtitles (if provided)
            logger.info("-" * 20 + " Step 2: Parse Subtitles " + "-" * 20)
            if args.srt_path:
                logger.info(f"Subtitle file provided: '{args.srt_path}' - will use subtitle verification mode.")
                subtitles = parse_subtitles(args.srt_path)
                if subtitles is None:
                    raise RuntimeError("Failed to parse subtitles.")
            else:
                logger.info("No subtitle file provided - will use AI-only detection mode.")
                subtitles = None

            if not self.processing:
                return

            # Update status
            self.root.after(0, lambda: self.status_label.config(text="Extracting audio..."))

            # 3. Extract Audio
            logger.info("-" * 20 + " Step 3: Extract Audio " + "-" * 20)
            if not extract_audio(args.video_path, TEMP_AUDIO_FILENAME):
                raise RuntimeError("Failed to extract audio from video.")

            if not self.processing:
                return

            # Update status
            self.root.after(0, lambda: self.status_label.config(text="Transcribing audio (this may take a while)..."))

            # 4. Transcribe Audio
            logger.info("-" * 20 + " Step 4: Transcribe Audio " + "-" * 20)
            word_timestamps = transcribe_audio(TEMP_AUDIO_FILENAME, args.model)
            if word_timestamps is None:
                raise RuntimeError("Audio transcription failed.")

            if not self.processing:
                return

            # Update status
            if subtitles:
                self.root.after(0, lambda: self.status_label.config(text="Verifying foul words..."))
            else:
                self.root.after(0, lambda: self.status_label.config(text="Processing foul words (AI-only)..."))

            # 5. Find Mute Segments (Verified or AI-only)
            if subtitles:
                logger.info("-" * 20 + " Step 5: Verify and Find Mute Segments " + "-" * 20)
            else:
                logger.info("-" * 20 + " Step 5: Find Mute Segments (AI-only) " + "-" * 20)
            results_data = find_mute_segments_verified(word_timestamps, foul_words or [], subtitles)
            mute_segments = results_data.get("mute_segments", [])

            if not self.processing:
                return

            # 6. Create Muted Video
            logger.info("-" * 20 + " Step 6: Create Muted Video " + "-" * 20)
            if mute_segments:
                # Update status
                self.root.after(0, lambda: self.status_label.config(text="Creating muted video..."))

                if not create_muted_video(args.video_path, args.output_path, mute_segments):
                    raise RuntimeError("Failed to create the final muted video.")
                else:
                    logger.info(f"Muted video successfully created at '{args.output_path}'")
                    success = True
            else:
                logger.info("No verified foul words found requiring muting. Output video was not created/modified.")
                success = True

        except Exception as e:
            logger.critical(f"An unexpected error occurred during processing: {e}", exc_info=True)
            success = False
        finally:
            # Cleanup
            logger.info("-" * 20 + " Step 7: Cleanup " + "-" * 20)
            if os.path.exists(TEMP_AUDIO_FILENAME):
                try:
                    os.remove(TEMP_AUDIO_FILENAME)
                    logger.info(f"Removed temporary audio file: '{TEMP_AUDIO_FILENAME}'")
                except Exception as e:
                    logger.warning(f"Could not remove temporary audio file '{TEMP_AUDIO_FILENAME}': {e}")

            # Generate report
            end_time = time.time()
            total_time = end_time - start_time

            if 'args' in locals():
                generate_report(args.report_file, args, results_data, total_time, success)
                logger.info(f"Report saved to: '{args.report_file}'")

            # Update UI on main thread
            if success:
                if mute_segments:
                    mode_text = "subtitle verification" if args.srt_path else "AI-only detection"
                    message = f"Processing completed successfully!\n\nMuted video saved to:\n{args.output_path}\n\nMode: {mode_text}\nProcessing time: {total_time:.2f} seconds"
                else:
                    mode_text = "subtitle verification" if args.srt_path else "AI-only detection"
                    message = f"Processing completed successfully!\n\nNo foul words required muting.\n\nMode: {mode_text}\nProcessing time: {total_time:.2f} seconds"
            else:
                message = f"Processing failed after {total_time:.2f} seconds.\nCheck the logs for details."

            self.root.after(0, lambda: self.processing_finished(success, message))


def run_gui():
    """Run the GUI version of the application."""
    root = tk.Tk()
    app = VideoCleanerGUI(root)
    root.mainloop()


def main():
    parser = argparse.ArgumentParser(
        description="Mute specified foul words in a video file. Optionally verify with an SRT subtitle file for improved accuracy. Generates log and report files.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter # Show defaults in help
    )
    parser.add_argument("--gui", action="store_true", help="Launch the GUI version of the application.")
    parser.add_argument("video_path", nargs="?", help="Path to the input video file.")
    parser.add_argument("srt_path", nargs="?", help="Path to the corresponding SRT subtitle file (optional - if not provided, uses AI-only detection).")
    parser.add_argument("-w", "--words_file", default="foul_words.txt", help="Path to the text file containing words to mute.")
    parser.add_argument("-m", "--model", default=DEFAULT_MODEL_SIZE, help="Whisper model size (e.g., tiny, base, small, medium, large).")
    parser.add_argument("-o", "--output_path", default=None, help="Path to save the output muted video file. Default: [input_filename]_muted_verified.[ext]")
    parser.add_argument("--report_file", default=None, help="Path to save the processing report. Default: [input_filename]_report.log")
    parser.add_argument("--log_file", default=None, help="Path to save the detailed processing log. Default: [input_filename]_processing.log")
    parser.add_argument("--log_level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], help="Set the logging level.")

    args = parser.parse_args()

    # Check if GUI mode is requested or if no arguments provided
    if args.gui or not args.video_path:
        run_gui()
        return

    # Validate required arguments for command-line mode
    if not args.video_path:
        parser.error("video_path is required when not using --gui mode")
        return

    # --- Determine File Paths ---
    # Use absolute paths for robustness
    args.video_path = os.path.abspath(args.video_path)

    # Handle optional subtitle file
    if args.srt_path:
        args.srt_path = os.path.abspath(args.srt_path)
    else:
        args.srt_path = None

    args.words_file = os.path.abspath(args.words_file)

    base_filename = os.path.splitext(os.path.basename(args.video_path))[0]
    input_dir = os.path.dirname(args.video_path) # Should be absolute now

    output_path = args.output_path
    if output_path:
        output_path = os.path.abspath(output_path) # Make output path absolute if provided
    else:
        vid_ext = os.path.splitext(args.video_path)[1]
        # Use different suffix based on whether subtitles are used
        suffix = OUTPUT_FILENAME_SUFFIX if args.srt_path else "_muted_ai"
        output_path = os.path.join(input_dir, f"{base_filename}{suffix}{vid_ext}")
    args.output_path = output_path # Store determined absolute path

    log_file_path = args.log_file
    if log_file_path:
         log_file_path = os.path.abspath(log_file_path)
    else:
        log_file_path = os.path.join(input_dir, f"{base_filename}{LOG_FILENAME_SUFFIX}")
    args.log_file = log_file_path # Store determined absolute path

    report_file_path = args.report_file
    if report_file_path:
         report_file_path = os.path.abspath(report_file_path)
    else:
        report_file_path = os.path.join(input_dir, f"{base_filename}{REPORT_FILENAME_SUFFIX}")
    args.report_file = report_file_path # Store determined absolute path

    # --- Setup Logging ---
    setup_logging(log_file_path, level=getattr(logging, args.log_level.upper()))

    logger.info("Starting script execution...")
    logger.info(f"Command line arguments (resolved paths): {vars(args)}")

    # --- Initial Checks ---
    start_time = time.time()
    overall_success = False
    results_data = {} # To store data for the report

    if not os.path.isfile(args.video_path):
        logger.critical(f"Input video file not found: '{args.video_path}'")
        generate_report(report_file_path, args, results_data, time.time() - start_time, False)
        sys.exit(1)

    # Check subtitle file only if provided
    if args.srt_path and not os.path.isfile(args.srt_path):
        logger.critical(f"Input subtitle file not found: '{args.srt_path}'")
        generate_report(report_file_path, args, results_data, time.time() - start_time, False)
        sys.exit(1)

    if not os.path.isfile(args.words_file):
         logger.critical(f"Foul words file not found: '{args.words_file}'")
         generate_report(report_file_path, args, results_data, time.time() - start_time, False)
         sys.exit(1)


    if os.path.exists(output_path):
        logger.warning(f"Output file '{output_path}' already exists.")
        try:
            overwrite = input(f"Output file '{output_path}' already exists. Overwrite? (y/N): ").strip().lower()
            if overwrite != 'y':
                logger.info("Operation cancelled by user.")
                generate_report(report_file_path, args, results_data, time.time() - start_time, False)
                sys.exit(0)
            else:
                 logger.info("User chose to overwrite the existing output file.")
                 # Attempt to remove the file before processing starts
                 try:
                     os.remove(output_path)
                     logger.info(f"Removed existing output file: '{output_path}'")
                 except Exception as e:
                      logger.error(f"Failed to remove existing output file '{output_path}': {e}. Processing aborted.")
                      generate_report(report_file_path, args, results_data, time.time() - start_time, False)
                      sys.exit(1)

        except EOFError: # Handle non-interactive environments
             logger.error("Output file exists and cannot prompt for overwrite in non-interactive mode. Please remove the existing file or specify a different output path.")
             generate_report(report_file_path, args, results_data, time.time() - start_time, False)
             sys.exit(1)


    # --- Main Processing Steps ---
    try:
        # 1. Load Foul Words
        logger.info("-" * 20 + " Step 1: Load Foul Words " + "-" * 20)
        foul_words = load_foul_words(args.words_file)
        if foul_words is None:
            raise RuntimeError("Failed to load foul words.")
        if not foul_words:
             logger.warning("Foul words list is empty. No words will be muted.")

        # 2. Parse Subtitles (if provided)
        logger.info("-" * 20 + " Step 2: Parse Subtitles " + "-" * 20)
        if args.srt_path:
            logger.info(f"Subtitle file provided: '{args.srt_path}' - will use subtitle verification mode.")
            subtitles = parse_subtitles(args.srt_path)
            if subtitles is None:
                raise RuntimeError("Failed to parse subtitles.")
        else:
            logger.info("No subtitle file provided - will use AI-only detection mode.")
            subtitles = None

        # 3. Extract Audio
        logger.info("-" * 20 + " Step 3: Extract Audio " + "-" * 20)
        if not extract_audio(args.video_path, TEMP_AUDIO_FILENAME):
            raise RuntimeError("Failed to extract audio from video.")

        # 4. Transcribe Audio
        logger.info("-" * 20 + " Step 4: Transcribe Audio " + "-" * 20)
        word_timestamps = transcribe_audio(TEMP_AUDIO_FILENAME, args.model)
        if word_timestamps is None:
            raise RuntimeError("Audio transcription failed.")

        # 5. Find Mute Segments (Verified or AI-only)
        if subtitles:
            logger.info("-" * 20 + " Step 5: Verify and Find Mute Segments " + "-" * 20)
        else:
            logger.info("-" * 20 + " Step 5: Find Mute Segments (AI-only) " + "-" * 20)
        results_data = find_mute_segments_verified(word_timestamps, foul_words or [], subtitles)
        mute_segments = results_data.get("mute_segments", [])

        # 6. Create Muted Video
        logger.info("-" * 20 + " Step 6: Create Muted Video " + "-" * 20)
        if mute_segments:
            # Pass mute_segments explicitly if needed by the inner function's scope
            # (Although it should be accessible via closure in this structure)
            if not create_muted_video(args.video_path, output_path, mute_segments):
                 raise RuntimeError("Failed to create the final muted video.")
            else:
                 logger.info(f"Muted video successfully created at '{output_path}'")
                 overall_success = True
        else:
            if subtitles:
                logger.info("No verified foul words found requiring muting. Output video was not created/modified.")
            else:
                logger.info("No AI-detected foul words found requiring muting. Output video was not created/modified.")
            overall_success = True

    except Exception as e:
        logger.critical(f"An unexpected error occurred during processing: {e}", exc_info=True)
        overall_success = False
    finally:
        # 7. Cleanup
        logger.info("-" * 20 + " Step 7: Cleanup " + "-" * 20)
        logger.info("Cleaning up temporary files...")
        if os.path.exists(TEMP_AUDIO_FILENAME):
            try:
                os.remove(TEMP_AUDIO_FILENAME)
                logger.info(f"Removed temporary audio file: '{TEMP_AUDIO_FILENAME}'")
            except Exception as e:
                logger.warning(f"Could not remove temporary audio file '{TEMP_AUDIO_FILENAME}': {e}")
        else:
             logger.info("Temporary audio file not found (may not have been created or already cleaned).")

        # 8. Final Report and Summary
        logger.info("-" * 20 + " Step 8: Final Report " + "-" * 20)
        end_time = time.time()
        total_time = end_time - start_time
        generate_report(report_file_path, args, results_data, total_time, overall_success)

        logger.info("-" * 50)
        if overall_success:
            if mute_segments:
                 logger.info(f"Processing finished successfully in {total_time:.2f} seconds.")
                 logger.info(f"Muted video saved to: '{output_path}'")
            else:
                if subtitles:
                    logger.info(f"Processing finished successfully in {total_time:.2f} seconds. No words required muting based on subtitle verification.")
                else:
                    logger.info(f"Processing finished successfully in {total_time:.2f} seconds. No words required muting based on AI detection.")
            logger.info(f"Detailed log saved to: '{log_file_path}'")
            logger.info(f"Summary report saved to: '{report_file_path}'")
            sys.exit(0)
        else:
            logger.error(f"Processing failed after {total_time:.2f} seconds.")
            logger.error(f"Please check the log file for details: '{log_file_path}'")
            logger.error(f"Summary report (may be incomplete) saved to: '{report_file_path}'")
            sys.exit(1)


if __name__ == "__main__":
    main()